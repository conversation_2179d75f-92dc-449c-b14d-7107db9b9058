//! # Facebook Video Extractor CLI
//!
//! A production-ready Facebook video extraction tool following Rust best practices.
//! This implementation serves as the foundation for WASM integration.
//!
//! ## Features
//! - Parallel DASH stream downloading with FFmpeg combination
//! - Intelligent stream detection and metadata analysis
//! - Audio caching optimization for multiple quality downloads
//! - Robust error handling and retry logic
//! - Clean filename generation with metadata integration
//! - WASM-compatible architecture with trait-based design
//!
//! ## Usage
//! ```bash
//! cargo run --bin test_facebook_extractor [URL]
//! cargo run --bin test_facebook_extractor --test-filenames
//! ```
//!
//! ## WASM Integration Notes
//! This CLI implementation provides the core extraction logic that can be
//! adapted for WASM by implementing the same traits with web-compatible methods.

use std::{env, fs, path::Path, collections::HashMap, time::Duration};
use tokio;
use reqwest;
use regex::Regex;
use base64::{Engine as _, engine::general_purpose};
use serde::{Deserialize, Serialize};
use log::{info, warn, error, debug};
use bytes::Bytes;

// ============================================================================
// CONSTANTS AND CONFIGURATION
// ============================================================================

/// Default Facebook video URL for testing
const DEFAULT_TEST_URL: &str = "https://www.facebook.com/yeah1stories/videos/1045988757255181";

/// HTTP client configuration constants
const DEFAULT_TIMEOUT_SECS: u64 = 600;
const CONNECTION_TIMEOUT_SECS: u64 = 30;
const POOL_IDLE_TIMEOUT_SECS: u64 = 90;
const POOL_MAX_IDLE_PER_HOST: usize = 10;
const TCP_KEEPALIVE_SECS: u64 = 60;
const MAX_REDIRECTS: usize = 5;

/// Download configuration constants
const MAX_RETRY_ATTEMPTS: usize = 5;
const CHUNK_TIMEOUT_SECS: u64 = 120;
const MAX_CONSECUTIVE_TIMEOUTS: usize = 3;
const MIN_FILE_SIZE_MB: u64 = 5;
const MAX_FILENAME_LENGTH: usize = 95;
const PROGRESS_UPDATE_INTERVAL_MB: u64 = 2;

/// Multi-threading configuration
const MIN_SIZE_FOR_MULTITHREADING_MB: u64 = 5;
const MAX_CONCURRENT_CHUNKS: usize = 6;
const MIN_CHUNK_SIZE_MB: u64 = 1;

/// Download timeout constants
const DOWNLOAD_CHUNK_TIMEOUT_SECS: u64 = 120;
const INITIAL_REQUEST_TIMEOUT_SECS: u64 = 300;
const HEAD_REQUEST_TIMEOUT_SECS: u64 = 30;
const SINGLE_THREADED_TIMEOUT_SECS: u64 = 180;

/// Progress and size constants
const PROGRESS_UPDATE_INTERVAL_BYTES: u64 = 1024 * 1024; // 1MB
const MULTITHREADING_MIN_SIZE_BYTES: u64 = 5 * 1024 * 1024; // 5MB

/// User agent strings for browser simulation
const USER_AGENTS: &[&str] = &[
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
];

/// CORS proxy services for fallback extraction
const PROXY_SERVICES: &[&str] = &[
    "https://proxy.cors.sh/",
    "https://corsproxy.io/?",
    "https://cors.bridged.cc/",
    "https://api.codetabs.com/v1/proxy?quest=",
];

/// Reserved Windows filenames that need special handling
const RESERVED_WINDOWS_NAMES: &[&str] = &[
    "CON", "PRN", "AUX", "NUL", "COM1", "COM2", "COM3", "COM4",
    "COM5", "COM6", "COM7", "COM8", "COM9", "LPT1", "LPT2",
    "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9"
];

// ============================================================================
// ERROR TYPES AND RESULT ALIASES
// ============================================================================

/// Custom error types for better error handling
#[derive(Debug, thiserror::Error)]
pub enum FacebookExtractorError {
    #[error("Invalid Facebook URL: {0}")]
    InvalidUrl(String),

    #[error("Video ID extraction failed: {0}")]
    VideoIdExtraction(String),

    #[error("Network request failed: {0}")]
    NetworkError(#[from] reqwest::Error),

    #[error("HTML parsing failed: {0}")]
    HtmlParsingError(String),

    #[error("Stream analysis failed: {0}")]
    StreamAnalysisError(String),

    #[error("Download failed: {0}")]
    DownloadError(String),

    #[error("FFmpeg operation failed: {0}")]
    FFmpegError(String),

    #[error("File system operation failed: {0}")]
    FileSystemError(#[from] std::io::Error),

    #[error("JSON parsing failed: {0}")]
    JsonError(#[from] serde_json::Error),

    #[error("Base64 decoding failed: {0}")]
    Base64Error(#[from] base64::DecodeError),

    #[error("Timeout occurred: {0}")]
    TimeoutError(String),

    #[error("Configuration error: {0}")]
    ConfigError(String),
}

impl From<String> for FacebookExtractorError {
    fn from(msg: String) -> Self {
        FacebookExtractorError::DownloadError(msg)
    }
}

impl FacebookExtractorError {
    /// Convert error to string for contains() method compatibility
    pub fn to_string(&self) -> String {
        format!("{}", self)
    }

    /// Check if error message contains a substring
    pub fn contains(&self, substring: &str) -> bool {
        self.to_string().contains(substring)
    }
}

/// Result type alias for cleaner error handling
pub type Result<T> = std::result::Result<T, FacebookExtractorError>;

// ============================================================================
// CONFIGURATION STRUCTURES
// ============================================================================

/// HTTP client configuration
#[derive(Debug, Clone)]
pub struct HttpConfig {
    pub timeout: Duration,
    pub connection_timeout: Duration,
    pub pool_idle_timeout: Duration,
    pub pool_max_idle_per_host: usize,
    pub tcp_keepalive: Duration,
    pub max_redirects: usize,
    pub user_agents: Vec<String>,
}

impl Default for HttpConfig {
    fn default() -> Self {
        Self {
            timeout: Duration::from_secs(DEFAULT_TIMEOUT_SECS),
            connection_timeout: Duration::from_secs(CONNECTION_TIMEOUT_SECS),
            pool_idle_timeout: Duration::from_secs(POOL_IDLE_TIMEOUT_SECS),
            pool_max_idle_per_host: POOL_MAX_IDLE_PER_HOST,
            tcp_keepalive: Duration::from_secs(TCP_KEEPALIVE_SECS),
            max_redirects: MAX_REDIRECTS,
            user_agents: USER_AGENTS.iter().map(|s| s.to_string()).collect(),
        }
    }
}

/// Download configuration
#[derive(Debug, Clone)]
pub struct DownloadConfig {
    pub max_retry_attempts: usize,
    pub chunk_timeout: Duration,
    pub max_consecutive_timeouts: usize,
    pub min_file_size_mb: u64,
    pub progress_update_interval_mb: u64,
    pub enable_multithreading: bool,
    pub min_size_for_multithreading_mb: u64,
    pub max_concurrent_chunks: usize,
    pub min_chunk_size_mb: u64,
}

impl Default for DownloadConfig {
    fn default() -> Self {
        Self {
            max_retry_attempts: MAX_RETRY_ATTEMPTS,
            chunk_timeout: Duration::from_secs(CHUNK_TIMEOUT_SECS),
            max_consecutive_timeouts: MAX_CONSECUTIVE_TIMEOUTS,
            min_file_size_mb: MIN_FILE_SIZE_MB,
            progress_update_interval_mb: PROGRESS_UPDATE_INTERVAL_MB,
            enable_multithreading: true,
            min_size_for_multithreading_mb: MIN_SIZE_FOR_MULTITHREADING_MB,
            max_concurrent_chunks: MAX_CONCURRENT_CHUNKS,
            min_chunk_size_mb: MIN_CHUNK_SIZE_MB,
        }
    }
}

/// Filename generation configuration
#[derive(Debug, Clone)]
pub struct FilenameConfig {
    pub max_length: usize,
    pub remove_hashtags: bool,
    pub remove_attribution: bool,
    pub sanitize_unicode: bool,
    pub handle_duplicates: bool,
}

impl Default for FilenameConfig {
    fn default() -> Self {
        Self {
            max_length: MAX_FILENAME_LENGTH,
            remove_hashtags: true,
            remove_attribution: true,
            sanitize_unicode: true,
            handle_duplicates: true,
        }
    }
}

/// Main extractor configuration
#[derive(Debug, Clone)]
pub struct ExtractorConfig {
    pub http: HttpConfig,
    pub download: DownloadConfig,
    pub filename: FilenameConfig,
    pub proxy_services: Vec<String>,
    pub enable_caching: bool,
    pub cache_directory: String,
}

impl Default for ExtractorConfig {
    fn default() -> Self {
        Self {
            http: HttpConfig::default(),
            download: DownloadConfig::default(),
            filename: FilenameConfig::default(),
            proxy_services: PROXY_SERVICES.iter().map(|s| s.to_string()).collect(),
            enable_caching: true,
            cache_directory: "cache".to_string(),
        }
    }
}

// ============================================================================
// DATA STRUCTURES
// ============================================================================

/// Stream type classification for Facebook videos
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum StreamType {
    /// Complete video with audio (progressive streams)
    CompleteVideoAudio,
    /// Video-only stream (DASH)
    VideoOnly,
    /// Audio-only stream (DASH)
    AudioOnly,
    /// Combined video+audio stream (created via FFmpeg)
    CombinedVideoAudio,
    /// Unknown or unclassified stream
    Unknown,
}

impl StreamType {
    /// Check if stream contains video
    pub fn has_video(&self) -> bool {
        matches!(self, Self::CompleteVideoAudio | Self::VideoOnly | Self::CombinedVideoAudio)
    }

    /// Check if stream contains audio
    pub fn has_audio(&self) -> bool {
        matches!(self, Self::CompleteVideoAudio | Self::AudioOnly | Self::CombinedVideoAudio)
    }

    /// Check if stream is complete (has both video and audio)
    pub fn is_complete(&self) -> bool {
        matches!(self, Self::CompleteVideoAudio | Self::CombinedVideoAudio)
    }
}

/// Video quality information with enhanced metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VideoQuality {
    pub quality: String,
    pub size: String,
    pub format: String,
    pub download_url: String,
    pub width: u32,
    pub height: u32,
    pub stream_type: StreamType,
    pub efg_metadata: String,
    pub estimated_size_mb: u32,
    pub bitrate_kbps: Option<u32>,
    pub fps: Option<u32>,
    pub codec: Option<String>,
}

impl VideoQuality {
    /// Calculate pixel count for quality comparison
    pub fn pixel_count(&self) -> u32 {
        self.width * self.height
    }

    /// Get quality score for sorting (higher is better)
    pub fn quality_score(&self) -> u32 {
        let pixel_score = self.pixel_count();
        let bitrate_score = self.bitrate_kbps.unwrap_or(0);
        let complete_bonus = if self.stream_type.is_complete() { 1000000 } else { 0 };

        pixel_score + bitrate_score + complete_bonus
    }

    /// Check if this quality is better than another
    pub fn is_better_than(&self, other: &VideoQuality) -> bool {
        self.quality_score() > other.quality_score()
    }
}

/// Enhanced video metadata with structured data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VideoMetadata {
    pub author: String,
    pub description: String,
    pub publish_date: String,
    pub likes: u64,
    pub comments: u64,
    pub views: u64,
    pub shares: u64,
    pub hashtags: Vec<String>,
    pub duration_seconds: Option<u32>,
    pub language: Option<String>,
    pub category: Option<String>,
}

impl Default for VideoMetadata {
    fn default() -> Self {
        Self {
            author: "Unknown Author".to_string(),
            description: "No description available".to_string(),
            publish_date: "Unknown date".to_string(),
            likes: 0,
            comments: 0,
            views: 0,
            shares: 0,
            hashtags: Vec::new(),
            duration_seconds: None,
            language: None,
            category: None,
        }
    }
}

/// Stream combination for DASH video+audio merging
#[derive(Debug, Clone)]
pub struct StreamCombination {
    pub video_stream: VideoQuality,
    pub audio_stream: VideoQuality,
    pub combined_quality: String,
    pub estimated_size_mb: u32,
}

impl StreamCombination {
    /// Create a new stream combination
    pub fn new(video_stream: VideoQuality, audio_stream: VideoQuality) -> Self {
        let combined_quality = format!("{} (Video+Audio Combined)", video_stream.quality);
        let estimated_size_mb = video_stream.estimated_size_mb + audio_stream.estimated_size_mb;

        Self {
            video_stream,
            audio_stream,
            combined_quality,
            estimated_size_mb,
        }
    }

    /// Generate combined download URL
    pub fn combined_url(&self) -> String {
        format!("COMBINED:{}|{}", self.video_stream.download_url, self.audio_stream.download_url)
    }
}

/// Complete video information structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VideoInfo {
    pub title: String,
    pub duration: String,
    pub thumbnail: String,
    pub qualities: Vec<VideoQuality>,
    pub video_id: String,
    pub metadata: VideoMetadata,
    pub extraction_timestamp: chrono::DateTime<chrono::Utc>,
    pub source_url: String,
}

impl VideoInfo {
    /// Create new video info with current timestamp
    pub fn new(title: String, video_id: String, source_url: String) -> Self {
        Self {
            title,
            duration: "Unknown duration".to_string(),
            thumbnail: String::new(),
            qualities: Vec::new(),
            video_id,
            metadata: VideoMetadata::default(),
            extraction_timestamp: chrono::Utc::now(),
            source_url,
        }
    }

    /// Get the best quality stream available
    pub fn best_quality(&self) -> Option<&VideoQuality> {
        self.qualities.iter().max_by_key(|q| q.quality_score())
    }

    /// Get all complete streams (video+audio)
    pub fn complete_streams(&self) -> Vec<&VideoQuality> {
        self.qualities.iter().filter(|q| q.stream_type.is_complete()).collect()
    }

    /// Get video-only streams
    pub fn video_only_streams(&self) -> Vec<&VideoQuality> {
        self.qualities.iter().filter(|q| q.stream_type == StreamType::VideoOnly).collect()
    }

    /// Get audio-only streams
    pub fn audio_only_streams(&self) -> Vec<&VideoQuality> {
        self.qualities.iter().filter(|q| q.stream_type == StreamType::AudioOnly).collect()
    }
}

// ============================================================================
// TRAITS FOR EXTENSIBILITY
// ============================================================================

/// Trait for video extraction functionality
pub trait VideoExtractor {
    async fn extract_video_info(&self, url: &str) -> Result<VideoInfo>;
    fn is_valid_url(&self, url: &str) -> bool;
    fn extract_video_id(&self, url: &str) -> Result<String>;
}

/// Trait for stream downloading functionality
pub trait StreamDownloader {
    async fn download_stream(&self, quality: &VideoQuality, output_path: &str) -> Result<String>;
    async fn download_combined_stream(&self, combination: &StreamCombination, output_path: &str) -> Result<String>;
}

/// Trait for metadata parsing functionality
pub trait MetadataParser {
    fn parse_metadata(&self, html: &str) -> Result<VideoMetadata>;
    fn extract_title(&self, html: &str, video_id: &str) -> String;
    fn extract_duration(&self, html: &str) -> Option<u32>;
}

// ============================================================================
// MAIN EXTRACTOR IMPLEMENTATION
// ============================================================================

/// Production-ready Facebook video extractor with configuration-driven approach
pub struct FacebookExtractorCLI {
    client: reqwest::Client,
    config: ExtractorConfig,
    cache: HashMap<String, VideoInfo>,
}

impl FacebookExtractorCLI {
    /// Create a new extractor with default configuration
    pub fn new() -> Result<Self> {
        Self::with_config(ExtractorConfig::default())
    }

    /// Create a new extractor with custom configuration
    pub fn with_config(config: ExtractorConfig) -> Result<Self> {
        let client = Self::build_http_client(&config.http)?;

        Ok(Self {
            client,
            config,
            cache: HashMap::new(),
        })
    }

    /// Build HTTP client with configuration
    fn build_http_client(http_config: &HttpConfig) -> Result<reqwest::Client> {
        let client = reqwest::Client::builder()
            .timeout(http_config.timeout)
            .connect_timeout(http_config.connection_timeout)
            .pool_idle_timeout(http_config.pool_idle_timeout)
            .pool_max_idle_per_host(http_config.pool_max_idle_per_host)
            .tcp_keepalive(http_config.tcp_keepalive)
            .redirect(reqwest::redirect::Policy::limited(http_config.max_redirects))
            .build()
            .map_err(|e| FacebookExtractorError::ConfigError(format!("Failed to create HTTP client: {}", e)))?;

        Ok(client)
    }

    /// Get primary user agent for requests
    fn primary_user_agent(&self) -> &str {
        self.config.http.user_agents.first()
            .map(|s| s.as_str())
            .unwrap_or(USER_AGENTS[0])
    }

    /// Create cache key for URL
    fn cache_key(&self, url: &str) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        url.hash(&mut hasher);
        format!("video_{}", hasher.finish())
    }

    /// Get cached video info if available and not expired
    fn get_cached_info(&self, url: &str) -> Option<&VideoInfo> {
        if !self.config.enable_caching {
            return None;
        }

        let cache_key = self.cache_key(url);
        self.cache.get(&cache_key).and_then(|info| {
            // Check if cache is still valid (1 hour expiry)
            let age = chrono::Utc::now().signed_duration_since(info.extraction_timestamp);
            if age.num_hours() < 1 {
                Some(info)
            } else {
                None
            }
        })
    }

    /// Cache video info
    fn cache_info(&mut self, url: &str, info: VideoInfo) {
        if self.config.enable_caching {
            let cache_key = self.cache_key(url);
            self.cache.insert(cache_key, info);
        }
    }
}

// ============================================================================
// TRAIT IMPLEMENTATIONS
// ============================================================================

impl VideoExtractor for FacebookExtractorCLI {
    /// Extract video information from Facebook URL with caching and structured logging
    async fn extract_video_info(&self, url: &str) -> Result<VideoInfo> {
        info!("Starting video extraction for: {}", url);

        // Check cache first
        if let Some(cached_info) = self.get_cached_info(url) {
            info!("Using cached video info for: {}", url);
            return Ok(cached_info.clone());
        }

        // Validate Facebook URL
        if !self.is_valid_url(url) {
            return Err(FacebookExtractorError::InvalidUrl(url.to_string()));
        }

        // Extract video ID
        let video_id = self.extract_video_id(url)?;
        info!("Extracted video ID: {}", video_id);

        // Try multiple extraction methods
        info!("Trying direct fetch with spoofed headers");
        match self.try_direct_fetch(url).await {
            Ok(mut info) => {
                info!("Direct fetch succeeded");
                info.video_id = video_id;
                info.source_url = url.to_string();
                return Ok(info);
            },
            Err(e) => {
                warn!("Direct fetch failed: {}", e);
            }
        }

        info!("Trying proxy services");
        match self.try_proxy_services(url).await {
            Ok(mut info) => {
                info!("Proxy services succeeded");
                info.video_id = video_id;
                info.source_url = url.to_string();
                return Ok(info);
            },
            Err(e) => {
                warn!("Proxy services failed: {}", e);
            }
        }

        info!("Trying mobile version");
        match self.try_mobile_version(url).await {
            Ok(mut info) => {
                info!("Mobile version succeeded");
                info.video_id = video_id;
                info.source_url = url.to_string();
                return Ok(info);
            },
            Err(e) => {
                warn!("Mobile version failed: {}", e);
            }
        }

        Err(FacebookExtractorError::HtmlParsingError("All extraction methods failed".to_string()))
    }

    /// Validate Facebook URL format
    fn is_valid_url(&self, url: &str) -> bool {
        url.contains("facebook.com") && (url.contains("/watch") || url.contains("/videos/"))
    }

    /// Extract video ID from Facebook URL using regex patterns
    fn extract_video_id(&self, url: &str) -> Result<String> {
        let patterns = [
            r"facebook\.com/watch\?.*v=(\d+)",
            r"facebook\.com/.*/videos/(\d+)",
            r"facebook\.com/reel/(\d+)",
        ];

        for pattern in &patterns {
            if let Ok(regex) = Regex::new(pattern) {
                if let Some(capture) = regex.captures(url) {
                    if let Some(id_match) = capture.get(1) {
                        return Ok(id_match.as_str().to_string());
                    }
                }
            }
        }

        Err(FacebookExtractorError::VideoIdExtraction("Could not extract video ID from URL".to_string()))
    }
}

impl FacebookExtractorCLI {
    /// Try direct fetch with spoofed browser headers
    async fn try_direct_fetch(&self, url: &str) -> Result<VideoInfo> {
        info!("Fetching with spoofed browser headers");

        let response = self.client
            .get(url)
            .header("User-Agent", self.primary_user_agent())
            .header("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
            .header("Accept-Language", "en-US,en;q=0.5")
            .header("DNT", "1")
            .header("Connection", "keep-alive")
            .header("Upgrade-Insecure-Requests", "1")
            .header("Sec-Fetch-Dest", "document")
            .header("Sec-Fetch-Mode", "navigate")
            .header("Sec-Fetch-Site", "none")
            .header("Cache-Control", "no-cache")
            .send()
            .await?;

        debug!("Response status: {}", response.status());

        if !response.status().is_success() {
            return Err(FacebookExtractorError::HtmlParsingError(
                format!("HTTP error: {}", response.status())
            ));
        }

        let bytes = response.bytes().await?;
        debug!("Response size: {} bytes", bytes.len());

        let html = self.decode_response_bytes(bytes)?;
        debug!("Final HTML length: {} characters", html.len());

        self.parse_facebook_html(&html, url).await
    }

    /// Try proxy services for extraction
    async fn try_proxy_services(&self, url: &str) -> Result<VideoInfo> {
        for (i, proxy) in self.config.proxy_services.iter().enumerate() {
            info!("Trying proxy {}/{}: {}", i + 1, self.config.proxy_services.len(), proxy);

            match self.try_single_proxy(proxy, url).await {
                Ok(info) => {
                    info!("Proxy {} succeeded!", proxy);
                    return Ok(info);
                },
                Err(e) => {
                    warn!("Proxy {} failed: {}", proxy, e);
                    // Add delay between proxy attempts
                    tokio::time::sleep(Duration::from_millis(500)).await;
                }
            }
        }

        Err(FacebookExtractorError::HtmlParsingError(
            "All proxy services failed".to_string()
        ))
    }

    /// Try mobile version of Facebook
    async fn try_mobile_version(&self, url: &str) -> Result<VideoInfo> {
        info!("Trying mobile version");
        let mobile_url = url.replace("www.facebook.com", "m.facebook.com");
        self.try_direct_fetch(&mobile_url).await
    }

    /// Try a single proxy service
    async fn try_single_proxy(&self, proxy: &str, url: &str) -> Result<VideoInfo> {
        let proxied_url = if proxy.contains("codetabs.com") || proxy.contains("corsproxy.io") {
            format!("{}{}", proxy, urlencoding::encode(url))
        } else {
            format!("{}{}", proxy, url)
        };

        debug!("Proxied URL: {}", proxied_url);

        let response = self.client
            .get(&proxied_url)
            .header("User-Agent", self.primary_user_agent())
            .header("Accept", "*/*")
            .send()
            .await?;

        if !response.status().is_success() {
            return Err(FacebookExtractorError::HtmlParsingError(
                format!("Proxy returned status: {}", response.status())
            ));
        }

        let html = response.text().await?;
        debug!("Proxy response length: {} characters", html.len());

        self.parse_facebook_html(&html, url).await
    }

    /// Decode response bytes handling compression
    fn decode_response_bytes(&self, bytes: Bytes) -> Result<String> {
        match String::from_utf8(bytes.to_vec()) {
            Ok(text) => {
                debug!("Successfully decoded as UTF-8");
                Ok(text)
            },
            Err(_) => {
                debug!("Response is not valid UTF-8, checking for compression");
                // Try to decompress if it looks like gzip
                if bytes.len() > 2 && bytes[0] == 0x1f && bytes[1] == 0x8b {
                    debug!("Detected gzip compression, decompressing");
                    self.decompress_gzip(&bytes)
                } else {
                    Err(FacebookExtractorError::HtmlParsingError("Response is in unknown binary format".to_string()))
                }
            }
        }
    }

    /// Decompress gzip data
    fn decompress_gzip(&self, data: &[u8]) -> Result<String> {
        use std::io::Read;
        use flate2::read::GzDecoder;

        let mut decoder = GzDecoder::new(data);
        let mut decompressed = String::new();
        decoder.read_to_string(&mut decompressed)
            .map_err(|e| FacebookExtractorError::HtmlParsingError(format!("Gzip decompression failed: {}", e)))?;

        Ok(decompressed)
    }









    /// Parse Facebook HTML for video URLs
    async fn parse_facebook_html(&self, html: &str, original_url: &str) -> Result<VideoInfo> {
        println!("🔍 Parsing HTML for video URLs...");

        let video_id = self.extract_video_id(original_url)?;

        // Debug: Show what we're working with
        println!("📊 HTML Analysis:");
        println!("  - Contains 'fbcdn.net': {}", html.contains("fbcdn.net"));
        println!("  - Contains '.mp4': {}", html.contains(".mp4"));
        println!("  - Contains 'video': {}", html.contains("video"));
        println!("  - Contains 'playable_url': {}", html.contains("playable_url"));

        // Save HTML to file for debugging
        if let Ok(mut file) = std::fs::File::create("debug_facebook.html") {
            use std::io::Write;
            let _ = file.write_all(html.as_bytes());
            println!("💾 Saved HTML to debug_facebook.html for inspection");
        }

        // Enhanced video URL patterns
        let video_url_patterns = vec![
            // Direct video URLs
            r#"https://video[^"]*\.fbcdn\.net/[^"]*\.mp4[^"]*"#,
            r#"https://scontent[^"]*\.fbcdn\.net/[^"]*\.mp4[^"]*"#,
            r#"https://[^"]*\.fbcdn\.net/[^"]*\.mp4[^"]*"#,

            // JSON embedded URLs
            r#""playable_url":"([^"]*\.mp4[^"]*)"#,
            r#""playable_url_quality_hd":"([^"]*\.mp4[^"]*)"#,
            r#""browser_native_hd_url":"([^"]*\.mp4[^"]*)"#,
            r#""browser_native_sd_url":"([^"]*\.mp4[^"]*)"#,
            r#""src":"([^"]*\.mp4[^"]*)"#,
            r#""url":"([^"]*\.mp4[^"]*)"#,

            // Alternative formats
            r#""dash_manifest":"([^"]*)"#,
            r#""hls_playlist":"([^"]*)"#,

            // Escaped URLs
            r#"https:\\\/\\\/[^"]*\.fbcdn\.net[^"]*\.mp4[^"]*"#,

            // Base64 or encoded URLs
            r#"data-src="([^"]*\.mp4[^"]*)"#,
            r#"src="([^"]*\.mp4[^"]*)"#,
        ];

        let mut found_urls = Vec::new();

        for (i, pattern) in video_url_patterns.iter().enumerate() {
            println!("🔍 Trying pattern {}/{}: {}", i + 1, video_url_patterns.len(), pattern);

            if let Ok(regex) = Regex::new(pattern) {
                let matches: Vec<_> = regex.captures_iter(html).collect();
                println!("  📊 Found {} matches", matches.len());

                for capture in matches {
                    if let Some(url_match) = capture.get(1).or_else(|| capture.get(0)) {
                        let mut url = url_match.as_str().replace("\\", "");

                        // Clean up escaped characters
                        url = url.replace("\\/", "/");
                        url = url.replace("\\u0026", "&");

                        if (url.contains(".mp4") || url.contains("dash") || url.contains("m3u8")) && !found_urls.contains(&url) {
                            println!("🎬 Found video URL: {}", url);
                            found_urls.push(url);
                        }
                    }
                }
            } else {
                println!("  ❌ Invalid regex pattern");
            }
        }

        // If no URLs found, try to find any fbcdn.net references
        if found_urls.is_empty() {
            println!("🔍 No video URLs found, searching for any fbcdn.net references...");

            if let Ok(regex) = Regex::new(r#"https://[^"]*\.fbcdn\.net[^"]*"#) {
                let fbcdn_matches: Vec<_> = regex.find_iter(html).collect();
                println!("📊 Found {} fbcdn.net references:", fbcdn_matches.len());

                for (i, m) in fbcdn_matches.iter().take(10).enumerate() {
                    println!("  {}. {}", i + 1, m.as_str());
                }

                if fbcdn_matches.len() > 10 {
                    println!("  ... and {} more", fbcdn_matches.len() - 10);
                }
            }
        }

        if !found_urls.is_empty() {
            println!("✅ Found {} video URLs", found_urls.len());

            // Analyze and filter video streams
            let all_qualities: Vec<VideoQuality> = found_urls.into_iter().map(|url| {
                self.analyze_facebook_video_stream(&url)
            }).collect();

            // Filter for complete video+audio streams only
            let complete_streams = self.filter_complete_video_streams(all_qualities);

            println!("🔍 Stream Analysis Complete:");
            println!("  ✅ Complete Video+Audio streams: {}", complete_streams.len());

            let qualities = complete_streams;

            // Extract comprehensive metadata
            let title = self.extract_title_from_html(html, &video_id);
            let metadata = self.extract_video_metadata(html);
            let duration = self.extract_duration_from_html(html);
            let thumbnail = self.extract_thumbnail_from_html(html);

            Ok(VideoInfo {
                title,
                duration,
                thumbnail,
                qualities,
                video_id,
                metadata,
                extraction_timestamp: chrono::Utc::now(),
                source_url: original_url.to_string(),
            })
        } else {
            Err(FacebookExtractorError::HtmlParsingError("No video URLs found in HTML response".to_string()))
        }
    }

    /// Extract title from HTML
    fn extract_title_from_html(&self, html: &str, video_id: &str) -> String {
        // Try to find title in various places
        let title_patterns = vec![
            r#"<title>([^<]+)</title>"#,
            r#""title":"([^"]+)""#,
            r#""name":"([^"]+)""#,
        ];

        for pattern in title_patterns {
            if let Ok(regex) = Regex::new(pattern) {
                if let Some(capture) = regex.captures(html) {
                    if let Some(title_match) = capture.get(1) {
                        let title = title_match.as_str().replace("\\", "");
                        if !title.is_empty() && title.len() > 5 {
                            return title;
                        }
                    }
                }
            }
        }

        format!("Facebook Video {}", &video_id[..8.min(video_id.len())])
    }

    /// Extract comprehensive video metadata from HTML
    fn extract_video_metadata(&self, html: &str) -> VideoMetadata {
        println!("🔍 Extracting video metadata...");

        let author = self.extract_author_from_html(html);
        let description = self.extract_description_from_html(html);
        let publish_date = self.extract_publish_date_from_html(html);
        let likes = self.extract_likes_from_html(html);
        let comments = self.extract_comments_from_html(html);
        let views = self.extract_views_from_html(html);
        let shares = self.extract_shares_from_html(html);
        let hashtags = self.extract_hashtags_from_html(html);

        println!("📊 Metadata extracted:");
        println!("  👤 Author: {}", author);
        println!("  📝 Description: {}", &description[..100.min(description.len())]);
        println!("  📅 Published: {}", publish_date);
        println!("  👍 Likes: {}", likes);
        println!("  💬 Comments: {}", comments);
        println!("  👁️ Views: {}", views);
        println!("  🔄 Shares: {}", shares);
        println!("  🏷️ Hashtags: {:?}", hashtags);

        VideoMetadata {
            author,
            description,
            publish_date,
            likes: likes.parse().unwrap_or(0),
            comments: comments.parse().unwrap_or(0),
            views: views.parse().unwrap_or(0),
            shares: shares.parse().unwrap_or(0),
            hashtags,
            duration_seconds: None,
            language: None,
            category: None,
        }
    }

    /// Extract author/page name from HTML
    fn extract_author_from_html(&self, html: &str) -> String {
        let patterns = vec![
            r#""name":"([^"]+)","url":"https://www\.facebook\.com/[^"]+""#,
            r#""author":\{"@type":"Person","name":"([^"]+)""#,
            r#"<title>([^|]+) \|"#,
            r#""page_name":"([^"]+)""#,
            r#""owner":\{"name":"([^"]+)""#,
        ];

        for pattern in patterns {
            if let Ok(regex) = Regex::new(pattern) {
                if let Some(capture) = regex.captures(html) {
                    if let Some(author_match) = capture.get(1) {
                        let author = author_match.as_str().replace("\\", "");
                        if !author.is_empty() && author.len() > 2 {
                            return author;
                        }
                    }
                }
            }
        }

        "Unknown Author".to_string()
    }

    /// Extract video description from HTML
    fn extract_description_from_html(&self, html: &str) -> String {
        let patterns = vec![
            r#""description":"([^"]+)""#,
            r#""text":"([^"]+)","ranges":\[\]"#,
            r#"<meta property="og:description" content="([^"]+)""#,
            r#""message":"([^"]+)""#,
            r#""story":"([^"]+)""#,
        ];

        for pattern in patterns {
            if let Ok(regex) = Regex::new(pattern) {
                if let Some(capture) = regex.captures(html) {
                    if let Some(desc_match) = capture.get(1) {
                        let mut description = desc_match.as_str().replace("\\", "");
                        description = description.replace("\\n", "\n");
                        description = description.replace("\\u0026", "&");
                        description = description.replace("\\u003C", "<");
                        description = description.replace("\\u003E", ">");

                        if !description.is_empty() && description.len() > 10 {
                            return description;
                        }
                    }
                }
            }
        }

        "No description available".to_string()
    }

    /// Extract publish date from HTML
    fn extract_publish_date_from_html(&self, html: &str) -> String {
        let patterns = vec![
            r#""publish_time":(\d+)"#,
            r#""creation_time":(\d+)"#,
            r#""datePublished":"([^"]+)""#,
            r#"data-utime="(\d+)""#,
            r#""timestamp":\{"time":(\d+)"#,
        ];

        for pattern in patterns {
            if let Ok(regex) = Regex::new(pattern) {
                if let Some(capture) = regex.captures(html) {
                    if let Some(time_match) = capture.get(1) {
                        let time_str = time_match.as_str();

                        // Try to parse as timestamp
                        if let Ok(timestamp) = time_str.parse::<i64>() {
                            // Convert timestamp to readable date
                            if timestamp > 1000000000 {
                                let datetime = chrono::DateTime::from_timestamp(timestamp, 0);
                                if let Some(dt) = datetime {
                                    return dt.format("%Y-%m-%d %H:%M:%S UTC").to_string();
                                }
                            }
                        } else {
                            // Return as-is if it's already a formatted date
                            return time_str.to_string();
                        }
                    }
                }
            }
        }

        "Unknown date".to_string()
    }

    /// Extract likes count from HTML
    fn extract_likes_from_html(&self, html: &str) -> String {
        let patterns = vec![
            r#""reaction_count":\{"count":(\d+)"#,
            r#""like_count":(\d+)"#,
            r#""reaction_count":(\d+)"#,
            r#"(\d+) likes"#,
            r#"(\d+) reactions"#,
        ];

        for pattern in patterns {
            if let Ok(regex) = Regex::new(pattern) {
                if let Some(capture) = regex.captures(html) {
                    if let Some(count_match) = capture.get(1) {
                        return count_match.as_str().to_string();
                    }
                }
            }
        }

        "0".to_string()
    }

    /// Extract comments count from HTML
    fn extract_comments_from_html(&self, html: &str) -> String {
        let patterns = vec![
            r#""comment_count":\{"total_count":(\d+)"#,
            r#""comments_count":(\d+)"#,
            r#""total_comment_count":(\d+)"#,
            r#"(\d+) comments"#,
        ];

        for pattern in patterns {
            if let Ok(regex) = Regex::new(pattern) {
                if let Some(capture) = regex.captures(html) {
                    if let Some(count_match) = capture.get(1) {
                        return count_match.as_str().to_string();
                    }
                }
            }
        }

        "0".to_string()
    }

    /// Extract views count from HTML
    fn extract_views_from_html(&self, html: &str) -> String {
        let patterns = vec![
            r#""view_count":(\d+)"#,
            r#""play_count":(\d+)"#,
            r#""video_view_count":(\d+)"#,
            r#"(\d+) views"#,
            r#"(\d+[\.,]\d+[KMB]?) views"#,
        ];

        for pattern in patterns {
            if let Ok(regex) = Regex::new(pattern) {
                if let Some(capture) = regex.captures(html) {
                    if let Some(count_match) = capture.get(1) {
                        return count_match.as_str().to_string();
                    }
                }
            }
        }

        "0".to_string()
    }

    /// Extract shares count from HTML
    fn extract_shares_from_html(&self, html: &str) -> String {
        let patterns = vec![
            r#""share_count":\{"count":(\d+)"#,
            r#""shares_count":(\d+)"#,
            r#"(\d+) shares"#,
        ];

        for pattern in patterns {
            if let Ok(regex) = Regex::new(pattern) {
                if let Some(capture) = regex.captures(html) {
                    if let Some(count_match) = capture.get(1) {
                        return count_match.as_str().to_string();
                    }
                }
            }
        }

        "0".to_string()
    }

    /// Extract hashtags from HTML
    fn extract_hashtags_from_html(&self, html: &str) -> Vec<String> {
        let mut hashtags = Vec::new();

        // Look for hashtags in various formats
        let patterns = vec![
            r#"#(\w+)"#,
            r#""hashtag":"([^"]+)""#,
            r#""tag":"(\w+)""#,
        ];

        for pattern in patterns {
            if let Ok(regex) = Regex::new(pattern) {
                for capture in regex.captures_iter(html) {
                    if let Some(tag_match) = capture.get(1) {
                        let hashtag = tag_match.as_str().to_string();
                        if !hashtags.contains(&hashtag) && hashtag.len() > 1 {
                            hashtags.push(hashtag);
                        }
                    }
                }
            }
        }

        hashtags
    }

    /// Extract video duration from HTML
    fn extract_duration_from_html(&self, html: &str) -> String {
        let patterns = vec![
            r#""duration_s":(\d+)"#,
            r#""duration":(\d+)"#,
            r#""length_seconds":(\d+)"#,
            r#""video_duration":(\d+)"#,
        ];

        for pattern in patterns {
            if let Ok(regex) = Regex::new(pattern) {
                if let Some(capture) = regex.captures(html) {
                    if let Some(duration_match) = capture.get(1) {
                        if let Ok(seconds) = duration_match.as_str().parse::<u32>() {
                            let minutes = seconds / 60;
                            let remaining_seconds = seconds % 60;
                            return format!("{}:{:02} ({} seconds)", minutes, remaining_seconds, seconds);
                        }
                    }
                }
            }
        }

        "Unknown duration".to_string()
    }

    /// Extract video thumbnail from HTML
    fn extract_thumbnail_from_html(&self, html: &str) -> String {
        let patterns = vec![
            r#""thumbnail":"([^"]+)""#,
            r#""image":"([^"]+\.jpg[^"]*)""#,
            r#"<meta property="og:image" content="([^"]+)""#,
            r#""preview_image":"([^"]+)""#,
        ];

        for pattern in patterns {
            if let Ok(regex) = Regex::new(pattern) {
                if let Some(capture) = regex.captures(html) {
                    if let Some(thumb_match) = capture.get(1) {
                        let thumbnail = thumb_match.as_str().replace("\\", "");
                        if thumbnail.starts_with("http") {
                            return thumbnail;
                        }
                    }
                }
            }
        }

        String::new()
    }

    /// Comprehensive analysis of Facebook video stream
    fn analyze_facebook_video_stream(&self, url: &str) -> VideoQuality {
        println!("🔍 Analyzing stream: {}", &url[..80.min(url.len())]);

        // Extract efg metadata from URL
        let efg_metadata = self.extract_efg_metadata(url);
        println!("📊 EFG metadata: {}", &efg_metadata[..100.min(efg_metadata.len())]);

        // Determine stream type from efg metadata
        let mut stream_type = self.determine_stream_type(&efg_metadata);

        // If EFG analysis failed, use URL pattern fallback
        if stream_type == StreamType::Unknown {
            stream_type = self.determine_stream_type_from_url(url);
        }

        println!("🎬 Final stream type: {:?}", stream_type);

        // Extract quality information
        let (quality_name, width, height) = if !efg_metadata.is_empty() {
            self.extract_quality_from_efg(&efg_metadata)
        } else {
            self.extract_quality_from_url(url)
        };
        println!("📺 Quality: {} ({}x{})", quality_name, width, height);

        // Estimate file size based on quality and duration
        let estimated_size_mb = self.estimate_file_size(width, height, 386); // 386 seconds from metadata
        println!("💾 Estimated size: {} MB", estimated_size_mb);

        VideoQuality {
            quality: quality_name,
            size: format!("~{} MB", estimated_size_mb),
            format: "MP4".to_string(),
            download_url: url.to_string(),
            width,
            height,
            stream_type,
            efg_metadata,
            estimated_size_mb,
            bitrate_kbps: None,
            fps: None,
            codec: None,
        }
    }

    /// Extract efg metadata from Facebook video URL
    fn extract_efg_metadata(&self, url: &str) -> String {
        if let Some(efg_start) = url.find("efg=") {
            let efg_part = &url[efg_start + 4..];
            let encoded_efg = if let Some(efg_end) = efg_part.find('&') {
                &efg_part[..efg_end]
            } else {
                efg_part
            };

            println!("🔍 Raw EFG: {}", &encoded_efg[..50.min(encoded_efg.len())]);

            // First URL decode
            if let Ok(url_decoded) = urlencoding::decode(encoded_efg) {
                println!("🔓 URL decoded: {}", &url_decoded[..100.min(url_decoded.len())]);

                // Try base64 decode
                if let Ok(base64_decoded) = general_purpose::STANDARD.decode(url_decoded.as_bytes()) {
                    if let Ok(json_string) = String::from_utf8(base64_decoded) {
                        println!("✅ Successfully decoded EFG JSON: {}", json_string);
                        return json_string;
                    }
                }

                // If base64 decode fails, try to extract quality info from the URL-decoded version
                println!("⚠️ Base64 decode failed, analyzing URL decoded version for quality info");

                // Check if the URL-decoded version contains useful quality information
                if url_decoded.contains("720") || url_decoded.contains("360") || url_decoded.contains("1080") ||
                   url_decoded.contains("540") || url_decoded.contains("480") || url_decoded.contains("240") {
                    println!("✅ Found quality info in URL-decoded EFG: {}", &url_decoded[..200.min(url_decoded.len())]);
                    return url_decoded.to_string();
                }

                // Even if no quality info found, return the URL-decoded version for pattern matching
                return url_decoded.to_string();
            }
        }
        String::new()
    }

    /// Determine stream type from efg metadata using dynamic JSON parsing
    fn determine_stream_type(&self, efg_metadata: &str) -> StreamType {
        println!("🔍 Analyzing EFG: {}", &efg_metadata[..100.min(efg_metadata.len())]);

        if efg_metadata.is_empty() {
            println!("⚠️ No EFG metadata - using fallback detection");
            return StreamType::Unknown;
        }

        // Try to parse as JSON first (for already decoded metadata)
        if let Some(stream_type) = self.parse_efg_json(efg_metadata) {
            return stream_type;
        }

        // If not JSON, try base64 decoding (for URL-encoded metadata)
        if efg_metadata.starts_with("eyJ") {
            if let Some(stream_type) = self.decode_and_parse_efg(efg_metadata) {
                return stream_type;
            }
        }

        // Final fallback to URL pattern analysis
        println!("⚠️ Could not parse EFG metadata, using intelligent fallback detection");

        // Check if the URL-encoded EFG contains audio indicators
        if efg_metadata.contains("audio") || efg_metadata.contains("heaac") || efg_metadata.contains("aac") {
            println!("🔊 Detected: Audio stream via intelligent fallback (contains audio keywords)");
            StreamType::AudioOnly
        } else if efg_metadata.contains("720p") || efg_metadata.contains("540p") || efg_metadata.contains("480p") || efg_metadata.contains("1080p") || efg_metadata.contains("360p") ||
                  efg_metadata.contains("NzIwcA") || efg_metadata.contains("NzIwcCIs") || // 720p in base64
                  efg_metadata.contains("NTQwcA") || efg_metadata.contains("NTQwcCIs") || // 540p in base64
                  efg_metadata.contains("NDgwcA") || efg_metadata.contains("NDgwcCIs") || // 480p in base64
                  efg_metadata.contains("MTA4MHA") || efg_metadata.contains("MTA4MHAi") || // 1080p in base64
                  efg_metadata.contains("MzYwcA") || efg_metadata.contains("MzYwcCIs") { // 360p in base64
            // Check for quality indicators that suggest video streams
            if efg_metadata.contains("progressive") {
                println!("✅ Detected: Progressive stream via quality indicator fallback");
                StreamType::CompleteVideoAudio
            } else {
                println!("📹 Detected: Video-only stream via quality indicator fallback");
                StreamType::VideoOnly
            }
        } else if efg_metadata.contains("dash_vp9") || efg_metadata.contains("dash_h264") || efg_metadata.contains("vp9") || efg_metadata.contains("h264") ||
                  efg_metadata.contains("ZGFzaF92cDk") || efg_metadata.contains("ZGFzaF9oMjY0") { // dash_vp9, dash_h264 in base64
            println!("📹 Detected: Video-only stream via codec fallback");
            StreamType::VideoOnly
        } else {
            println!("❓ Unknown stream type, assuming audio for parallel download");
            StreamType::AudioOnly  // Assume unknown streams are audio for parallel download
        }
    }

    /// Parse EFG JSON content to determine stream type
    fn parse_efg_json(&self, json_content: &str) -> Option<StreamType> {
        // Try to parse as JSON
        if let Ok(json_value) = serde_json::from_str::<serde_json::Value>(json_content) {
            if let Some(vencode_tag) = json_value.get("vencode_tag").and_then(|v| v.as_str()) {
                println!("🔍 Found vencode_tag: {}", vencode_tag);

                // Analyze vencode_tag to determine stream type
                if vencode_tag.contains("xpv_progressive") {
                    println!("✅ Detected: Progressive stream (complete video+audio)");
                    return Some(StreamType::CompleteVideoAudio);
                } else if vencode_tag.contains("dash_ln_heaac") || vencode_tag.ends_with("_audio") {
                    println!("🔊 Detected: Audio-only stream");
                    return Some(StreamType::AudioOnly);
                } else if vencode_tag.contains("dash_vp9") || vencode_tag.contains("dash_h264") {
                    if vencode_tag.contains("_audio") {
                        println!("🔊 Detected: DASH audio stream");
                        return Some(StreamType::AudioOnly);
                    } else {
                        println!("📹 Detected: DASH video-only stream (NO AUDIO)");
                        return Some(StreamType::VideoOnly);
                    }
                } else {
                    println!("❓ Unknown vencode_tag pattern: {}", vencode_tag);
                    return Some(StreamType::Unknown);
                }
            } else {
                println!("⚠️ No vencode_tag found in JSON");
            }
        } else {
            // Not valid JSON, check for simple string patterns
            if json_content.contains("xpv_progressive") {
                println!("✅ Detected: Progressive stream from string pattern");
                return Some(StreamType::CompleteVideoAudio);
            } else if json_content.contains("dash_ln_heaac") || json_content.contains("_audio") {
                println!("🔊 Detected: Audio stream from string pattern");
                return Some(StreamType::AudioOnly);
            } else if json_content.contains("dash_vp9") || json_content.contains("dash_h264") {
                println!("📹 Detected: Video-only stream from string pattern");
                return Some(StreamType::VideoOnly);
            }
        }

        None
    }

    /// Decode base64 EFG metadata and parse the JSON content
    fn decode_and_parse_efg(&self, encoded_metadata: &str) -> Option<StreamType> {
        println!("🔓 Attempting base64 decode of EFG metadata...");

        if let Ok(decoded_bytes) = general_purpose::STANDARD.decode(encoded_metadata.as_bytes()) {
            if let Ok(json_str) = String::from_utf8(decoded_bytes) {
                println!("✅ Base64 decode successful: {}", &json_str[..100.min(json_str.len())]);
                return self.parse_efg_json(&json_str);
            } else {
                println!("❌ Base64 decoded but not valid UTF-8");
            }
        } else {
            println!("❌ Base64 decode failed");
        }

        // If base64 decode fails, try direct string pattern matching on the encoded metadata
        println!("🔍 Trying direct pattern matching on URL-encoded EFG...");
        println!("🔍 Checking for audio patterns in: {}", &encoded_metadata[..100.min(encoded_metadata.len())]);

        if encoded_metadata.contains("dash_ln_heaac_vbr3_audio") {
            println!("🔊 Detected: Audio stream via direct pattern matching (dash_ln_heaac_vbr3_audio)");
            return Some(StreamType::AudioOnly);
        } else if encoded_metadata.contains("ImRhc2hfbG5faGVhYWNfdmJyM19hdWRpbw") {
            println!("🔊 Detected: Audio stream via base64 pattern matching (dash_ln_heaac_vbr3_audio)");
            return Some(StreamType::AudioOnly);
        } else if encoded_metadata.contains("ZGFzaF9sbl9oZWFhY192YnIzX2F1ZGlv") {
            println!("🔊 Detected: Audio stream via base64 pattern matching (dash_ln_heaac_vbr3_audio without quotes)");
            return Some(StreamType::AudioOnly);
        } else if encoded_metadata.contains("dash_ln_heaac") {
            println!("🔊 Detected: Audio stream via direct pattern matching (dash_ln_heaac)");
            return Some(StreamType::AudioOnly);
        } else if encoded_metadata.contains("dash_vp9") || encoded_metadata.contains("dash_h264") {
            if encoded_metadata.contains("_audio") {
                println!("🔊 Detected: DASH audio stream via pattern matching");
                return Some(StreamType::AudioOnly);
            } else {
                println!("📹 Detected: DASH video-only stream via pattern matching");
                return Some(StreamType::VideoOnly);
            }
        } else if encoded_metadata.contains("xpv_progressive") {
            println!("✅ Detected: Progressive stream via pattern matching");
            return Some(StreamType::CompleteVideoAudio);
        }

        println!("❌ No patterns matched in direct pattern matching");

        None
    }

    /// Fallback stream type detection using URL patterns
    fn determine_stream_type_from_url(&self, url: &str) -> StreamType {
        println!("🔍 Fallback URL analysis for: {}", &url[..100.min(url.len())]);

        // Look for quality indicators that suggest complete streams
        if url.contains("browser_native_hd_url") || url.contains("browser_native_sd_url") {
            println!("✅ Detected: Browser native stream (likely complete video+audio)");
            StreamType::CompleteVideoAudio
        } else if url.contains("/m69/") {
            // m69 format often indicates complete streams
            println!("✅ Detected: m69 format (likely complete video+audio)");
            StreamType::CompleteVideoAudio
        } else {
            println!("❓ Unknown URL pattern");
            StreamType::Unknown
        }
    }

    /// Extract quality information from efg metadata
    fn extract_quality_from_efg(&self, efg_metadata: &str) -> (String, u32, u32) {
        println!("🔍 Quality extraction from EFG: {}", &efg_metadata[..100.min(efg_metadata.len())]);

        // Look for quality indicators in efg metadata (including base64-encoded patterns)
        if efg_metadata.contains("1080p") || efg_metadata.contains("_1080p") || efg_metadata.contains("MTA4MHA") || efg_metadata.contains("MTA4MHAi") {
            println!("✅ Found 1080p quality indicator");
            ("1080p Full HD".to_string(), 1920, 1080)
        } else if efg_metadata.contains("720p") || efg_metadata.contains(".720.") || efg_metadata.contains("_720p") || efg_metadata.contains("C3.720") || efg_metadata.contains("720") ||
                  efg_metadata.contains("NzIwcA") || efg_metadata.contains("NzIwcCIs") {
            println!("✅ Found 720p quality indicator");
            ("720p HD".to_string(), 1280, 720)
        } else if efg_metadata.contains("540p") || efg_metadata.contains("_540p") || efg_metadata.contains("540") ||
                  efg_metadata.contains("NTQwcA") || efg_metadata.contains("NTQwcCIs") {
            println!("✅ Found 540p quality indicator");
            ("540p".to_string(), 960, 540)
        } else if efg_metadata.contains("480p") || efg_metadata.contains("_480p") || efg_metadata.contains("480") ||
                  efg_metadata.contains("NDgwcA") || efg_metadata.contains("NDgwcCIs") {
            println!("✅ Found 480p quality indicator");
            ("480p SD".to_string(), 854, 480)
        } else if efg_metadata.contains("360p") || efg_metadata.contains(".360.") || efg_metadata.contains("_360p") || efg_metadata.contains("C3.360") || efg_metadata.contains("360") ||
                  efg_metadata.contains("MzYwcA") || efg_metadata.contains("MzYwcCIs") {
            println!("✅ Found 360p quality indicator");
            ("360p".to_string(), 640, 360)
        } else if efg_metadata.contains("240p") || efg_metadata.contains("_240p") || efg_metadata.contains("240") {
            println!("✅ Found 240p quality indicator");
            ("240p".to_string(), 426, 240)
        } else {
            println!("⚠️ No quality indicator found, using Original");
            ("Original".to_string(), 0, 0)
        }
    }

    /// Extract quality information from URL patterns (fallback)
    fn extract_quality_from_url(&self, url: &str) -> (String, u32, u32) {
        // Look for quality indicators in URL
        if url.contains("browser_native_hd_url") {
            ("720p HD (Native)".to_string(), 1280, 720)
        } else if url.contains("browser_native_sd_url") {
            ("360p SD (Native)".to_string(), 640, 360)
        } else if url.contains("1080") {
            ("1080p Full HD".to_string(), 1920, 1080)
        } else if url.contains("720") {
            ("720p HD".to_string(), 1280, 720)
        } else if url.contains("480") {
            ("480p SD".to_string(), 854, 480)
        } else if url.contains("360") {
            ("360p".to_string(), 640, 360)
        } else {
            ("Original Quality".to_string(), 1280, 720) // Default assumption for unknown
        }
    }

    /// Estimate file size based on resolution and duration
    fn estimate_file_size(&self, width: u32, height: u32, duration_seconds: u32) -> u32 {
        if width == 0 || height == 0 {
            return 10; // Default estimate for unknown resolution
        }

        // Rough bitrate estimates (kbps) based on resolution
        let video_bitrate = match (width, height) {
            (1920, 1080) => 8000,  // 1080p: ~8 Mbps
            (1280, 720) => 5000,   // 720p: ~5 Mbps
            (960, 540) => 2500,    // 540p: ~2.5 Mbps
            (854, 480) => 1500,    // 480p: ~1.5 Mbps
            (640, 360) => 1000,    // 360p: ~1 Mbps
            _ => 2000,             // Default: ~2 Mbps
        };

        let audio_bitrate = 128; // ~128 kbps for audio
        let total_bitrate = video_bitrate + audio_bitrate;

        // Calculate file size: (bitrate in kbps * duration in seconds) / 8 / 1024
        let size_mb = (total_bitrate * duration_seconds) / 8 / 1024;
        size_mb.max(1) // Minimum 1 MB
    }

    /// Enhanced filtering with DASH stream combination support
    fn filter_complete_video_streams(&self, all_streams: Vec<VideoQuality>) -> Vec<VideoQuality> {
        println!("\n🔍 Enhanced filtering for complete video+audio content...");

        // Separate streams by type
        let mut progressive_streams = Vec::new();
        let mut video_only_streams = Vec::new();
        let mut audio_only_streams = Vec::new();

        for (i, stream) in all_streams.iter().enumerate() {
            println!("📊 Analyzing stream {}: {} - {:?} - {} MB",
                i + 1, stream.quality, stream.stream_type, stream.estimated_size_mb);
            println!("    🔍 EFG: {}", &stream.efg_metadata[..100.min(stream.efg_metadata.len())]);
            println!("    🔗 URL: {}", &stream.download_url[..100.min(stream.download_url.len())]);

            match stream.stream_type {
                StreamType::CompleteVideoAudio => {
                    if stream.estimated_size_mb >= 5 {
                        progressive_streams.push(stream.clone());
                        println!("    ✅ Added to progressive streams");
                    } else {
                        println!("    ⏭️ Skipped: too small ({} MB)", stream.estimated_size_mb);
                    }
                },
                StreamType::VideoOnly => {
                    video_only_streams.push(stream.clone());
                    println!("    📹 Added to video-only streams");
                },
                StreamType::AudioOnly => {
                    audio_only_streams.push(stream.clone());
                    println!("    🔊 Added to audio-only streams");
                },
                _ => {
                    println!("    ⏭️ Skipping unknown stream type: {:?}", stream.stream_type);
                }
            }
        }

        println!("\n📊 Stream Analysis:");
        println!("  ✅ Progressive (complete) streams: {}", progressive_streams.len());
        println!("  📹 Video-only streams: {}", video_only_streams.len());
        println!("  🔊 Audio-only streams: {}", audio_only_streams.len());

        // Create combined streams from DASH video + audio
        let combined_streams = self.create_combined_streams(&video_only_streams, &audio_only_streams);

        // Combine all available streams
        let mut final_streams = progressive_streams;
        final_streams.extend(combined_streams);

        // Remove duplicates and sort by quality
        final_streams = self.deduplicate_and_sort_streams(final_streams);

        println!("\n📋 Final available streams:");
        for (i, stream) in final_streams.iter().enumerate() {
            let stream_source = match stream.stream_type {
                StreamType::CompleteVideoAudio => "Progressive",
                StreamType::CombinedVideoAudio => "DASH Combined",
                _ => "Unknown"
            };
            println!("  {}. {} - {} MB ({})",
                i + 1, stream.quality, stream.estimated_size_mb, stream_source);
        }

        final_streams
    }

    /// Create combined video+audio streams from DASH components
    fn create_combined_streams(&self, video_streams: &[VideoQuality], audio_streams: &[VideoQuality]) -> Vec<VideoQuality> {
        println!("\n🔧 Creating combined DASH streams...");

        if video_streams.is_empty() || audio_streams.is_empty() {
            println!("⚠️ Cannot create combined streams: missing video or audio components");
            return Vec::new();
        }

        // Find the best audio stream
        let best_audio = audio_streams.iter()
            .max_by_key(|stream| stream.estimated_size_mb)
            .unwrap();

        println!("🔊 Selected audio stream: {} ({} MB)", best_audio.quality, best_audio.estimated_size_mb);

        let mut combined_streams = Vec::new();

        // Create combinations for each video quality
        for video_stream in video_streams {
            if video_stream.width > 0 && video_stream.height > 0 {
                let combined_quality = format!("{} (Video+Audio Combined)", video_stream.quality);
                let combined_size = video_stream.estimated_size_mb + best_audio.estimated_size_mb;

                // Clean URLs before combining to avoid corruption
                let clean_video_url = self.clean_facebook_url(&video_stream.download_url);
                let clean_audio_url = self.clean_facebook_url(&best_audio.download_url);

                println!("🔧 Cleaned video URL: {}", &clean_video_url[..80.min(clean_video_url.len())]);
                println!("🔧 Cleaned audio URL: {}", &clean_audio_url[..80.min(clean_audio_url.len())]);

                let combined_stream = VideoQuality {
                    quality: combined_quality,
                    size: format!("~{} MB", combined_size),
                    format: "MP4".to_string(),
                    download_url: format!("COMBINED:{}|{}", clean_video_url, clean_audio_url),
                    width: video_stream.width,
                    height: video_stream.height,
                    stream_type: StreamType::CombinedVideoAudio,
                    efg_metadata: format!("COMBINED:{}+{}", video_stream.efg_metadata, best_audio.efg_metadata),
                    estimated_size_mb: combined_size,
                    bitrate_kbps: None,
                    fps: None,
                    codec: None,
                };

                println!("✅ Created combined stream: {} ({} MB)",
                    combined_stream.quality, combined_stream.estimated_size_mb);
                combined_streams.push(combined_stream);
            }
        }

        combined_streams
    }

    /// Remove duplicates and sort streams by quality
    fn deduplicate_and_sort_streams(&self, streams: Vec<VideoQuality>) -> Vec<VideoQuality> {
        let mut unique_streams = Vec::new();
        let mut seen_qualities = std::collections::HashSet::new();

        for stream in streams {
            let quality_key = format!("{}x{}", stream.width, stream.height);
            if !seen_qualities.contains(&quality_key) {
                seen_qualities.insert(quality_key);
                unique_streams.push(stream);
            }
        }

        // Sort by quality (highest resolution first)
        unique_streams.sort_by(|a, b| {
            let a_pixels = a.width * a.height;
            let b_pixels = b.width * b.height;
            b_pixels.cmp(&a_pixels)
        });

        unique_streams
    }

    /// Validate Facebook URL
    fn is_valid_facebook_url(&self, url: &str) -> bool {
        url.contains("facebook.com") && (url.contains("/watch") || url.contains("/videos/"))
    }

    /// Extract video ID from Facebook URL
    fn extract_video_id(&self, url: &str) -> Result<String> {
        let patterns = vec![
            r"facebook\.com/watch\?.*v=(\d+)",
            r"facebook\.com/.*/videos/(\d+)",
            r"facebook\.com/reel/(\d+)",
        ];

        for pattern in patterns {
            if let Ok(regex) = Regex::new(pattern) {
                if let Some(capture) = regex.captures(url) {
                    if let Some(id_match) = capture.get(1) {
                        return Ok(id_match.as_str().to_string());
                    }
                }
            }
        }

        Err(FacebookExtractorError::VideoIdExtraction("Could not extract video ID from URL".to_string()))
    }

    /// Download video to local file (supports combined DASH streams) with enhanced filename strategy
    async fn download_video(&self, url: &str, download_dir: &str, index: usize, quality: &str) -> Result<String> {
        // Check if this is a combined DASH stream
        if url.starts_with("COMBINED:") {
            return self.download_combined_stream(url, download_dir, index, quality).await;
        }

        // Regular single stream download
        self.download_single_stream(url, download_dir, index, quality).await
    }

    /// Download video with enhanced filename strategy (requires video info for metadata)
    async fn download_video_with_metadata(&self, url: &str, download_dir: &str, index: usize, quality: &str, video_info: &VideoInfo) -> Result<String> {
        // Check if this is a combined DASH stream
        if url.starts_with("COMBINED:") {
            return self.download_combined_stream_with_metadata(url, download_dir, index, quality, video_info).await;
        }

        // Regular single stream download with metadata
        self.download_single_stream_with_metadata(url, download_dir, index, quality, video_info, "").await
    }

    /// Download and combine DASH video + audio streams using optimized audio caching
    async fn download_combined_stream(&self, combined_url: &str, download_dir: &str, index: usize, quality: &str) -> Result<String> {
        println!("� Processing combined DASH stream with audio caching optimization: {}", quality);

        // Parse combined URL
        let urls_part = &combined_url[9..]; // Remove "COMBINED:" prefix
        let parts: Vec<&str> = urls_part.split('|').collect();
        if parts.len() != 2 {
            return Err(FacebookExtractorError::InvalidUrl("Invalid combined URL format".to_string()));
        }

        // Clean the individual URLs to remove any corruption
        let video_url = self.clean_facebook_url(parts[0]);
        let audio_url = self.clean_facebook_url(parts[1]);

        println!("📹 Video URL: {}", &video_url[..80.min(video_url.len())]);
        println!("🔊 Audio URL: {}", &audio_url[..80.min(audio_url.len())]);

        // Create temporary filenames
        let safe_quality = quality.replace(" ", "_").replace("(", "").replace(")", "").to_lowercase();
        let final_output = format!("{}/video_{}_{}_combined.mp4", download_dir, index, safe_quality);

        // Check if final file already exists
        if Path::new(&final_output).exists() {
            println!("⏭️ Combined file already exists, skipping: {}", final_output);
            return Ok(final_output);
        }

        let start_time = std::time::Instant::now();

        // Step 1: Download audio first (prioritize smaller files for efficiency)
        println!("🎵 Step 1: Downloading audio stream first (optimization)...");
        let audio_cache_path = self.get_or_download_cached_audio(&audio_url, download_dir).await?;
        let audio_download_time = start_time.elapsed();
        println!("⏱️ Audio download completed in {:.2} seconds", audio_download_time.as_secs_f64());

        // Step 2: Download video stream while audio is cached
        println!("📹 Step 2: Downloading video stream...");
        let video_start_time = std::time::Instant::now();
        let temp_video_quality = format!("temp_video_{}", safe_quality);
        let video_result = self.download_single_stream_optimized(&video_url, download_dir, index, &temp_video_quality, "video").await;
        let video_download_time = video_start_time.elapsed();
        println!("⏱️ Video download completed in {:.2} seconds", video_download_time.as_secs_f64());

        let actual_video_path = match video_result {
            Ok(path) => path,
            Err(e) => return Err(FacebookExtractorError::DownloadError(format!("Failed to download video stream: {}", e)))
        };

        // Step 3: Combine using FFmpeg
        println!("🔧 Step 3: Combining video and cached audio streams with FFmpeg...");
        let combine_start = std::time::Instant::now();
        let ffmpeg_result = self.combine_streams_with_ffmpeg(&actual_video_path, &audio_cache_path, &final_output).await;
        let combine_duration = combine_start.elapsed();
        println!("⏱️ FFmpeg combination completed in {:.2} seconds", combine_duration.as_secs_f64());

        // Clean up temporary video file (keep audio cached for reuse)
        let _ = std::fs::remove_file(&actual_video_path);

        match ffmpeg_result {
            Ok(_) => {
                let total_duration = start_time.elapsed();
                println!("✅ Successfully combined streams: {}", final_output);
                println!("⏱️ Total process time: {:.2} seconds (audio: {:.2}s + video: {:.2}s + combine: {:.2}s)",
                    total_duration.as_secs_f64(), audio_download_time.as_secs_f64(), video_download_time.as_secs_f64(), combine_duration.as_secs_f64());

                // Verify the combined file was created and has reasonable size
                if let Ok(metadata) = std::fs::metadata(&final_output) {
                    let combined_size_mb = metadata.len() / 1024 / 1024;
                    println!("📊 Combined file size: {} MB", combined_size_mb);

                    // Calculate performance metrics
                    let total_download_time = audio_download_time.as_secs_f64() + video_download_time.as_secs_f64();
                    let download_speed = if total_download_time > 0.0 {
                        combined_size_mb as f64 / total_download_time
                    } else {
                        0.0
                    };
                    println!("🚀 Performance: {:.1} MB/s average download speed", download_speed);
                    println!("💾 Audio caching optimization: Reusing cached audio for subsequent downloads");

                    if combined_size_mb > 5 {
                        println!("✅ Optimized download + FFmpeg combination successful - file appears complete");
                    } else {
                        println!("⚠️ Combined file seems small - please verify it plays correctly");
                    }
                } else {
                    println!("⚠️ Could not verify combined file size");
                }

                Ok(final_output)
            },
            Err(e) => {
                println!("❌ FFmpeg combination failed: {}", e);
                Err(FacebookExtractorError::FFmpegError(format!("FFmpeg combination failed: {}", e)))
            }
        }
    }

    /// Download and combine DASH video + audio streams with enhanced filename strategy
    async fn download_combined_stream_with_metadata(&self, combined_url: &str, download_dir: &str, index: usize, quality: &str, video_info: &VideoInfo) -> Result<String> {
        println!("🎬 Processing combined DASH stream with enhanced filename: {}", quality);

        // Parse combined URL
        let urls_part = &combined_url[9..]; // Remove "COMBINED:" prefix
        let parts: Vec<&str> = urls_part.split('|').collect();
        if parts.len() != 2 {
            return Err(FacebookExtractorError::InvalidUrl("Invalid combined URL format".to_string()));
        }

        // Clean the individual URLs to remove any corruption
        let video_url = self.clean_facebook_url(parts[0]);
        let audio_url = self.clean_facebook_url(parts[1]);

        println!("📹 Video URL: {}", &video_url[..80.min(video_url.len())]);
        println!("🔊 Audio URL: {}", &audio_url[..80.min(audio_url.len())]);

        // Create descriptive filename using video metadata
        let final_output = self.create_descriptive_filename(video_info, quality, "combined", download_dir, index);

        // Check if final file already exists
        if Path::new(&final_output).exists() {
            println!("⏭️ Combined file already exists, skipping: {}", final_output);
            return Ok(final_output);
        }

        let start_time = std::time::Instant::now();

        // Step 1: Download audio first (prioritize smaller files for efficiency)
        println!("🎵 Step 1: Downloading audio stream first (optimization)...");
        let audio_cache_path = self.get_or_download_cached_audio(&audio_url, download_dir).await?;
        let audio_download_time = start_time.elapsed();
        println!("⏱️ Audio download completed in {:.2} seconds", audio_download_time.as_secs_f64());

        // Step 2: Download video stream while audio is cached
        println!("📹 Step 2: Downloading video stream...");
        let video_start_time = std::time::Instant::now();
        let temp_video_quality = format!("temp_video_{}", index);
        let video_result = self.download_single_stream_optimized(&video_url, download_dir, index, &temp_video_quality, "video").await;
        let video_download_time = video_start_time.elapsed();
        println!("⏱️ Video download completed in {:.2} seconds", video_download_time.as_secs_f64());

        let actual_video_path = match video_result {
            Ok(path) => path,
            Err(e) => return Err(FacebookExtractorError::DownloadError(format!("Failed to download video stream: {}", e)))
        };

        // Step 3: Combine using FFmpeg
        println!("🔧 Step 3: Combining video and cached audio streams with FFmpeg...");
        let combine_start = std::time::Instant::now();
        let ffmpeg_result = self.combine_streams_with_ffmpeg(&actual_video_path, &audio_cache_path, &final_output).await;
        let combine_duration = combine_start.elapsed();
        println!("⏱️ FFmpeg combination completed in {:.2} seconds", combine_duration.as_secs_f64());

        // Clean up temporary video file (keep audio cached for reuse)
        let _ = std::fs::remove_file(&actual_video_path);

        match ffmpeg_result {
            Ok(_) => {
                let total_duration = start_time.elapsed();
                println!("✅ Successfully combined streams with descriptive filename: {}", final_output);
                println!("⏱️ Total process time: {:.2} seconds (audio: {:.2}s + video: {:.2}s + combine: {:.2}s)",
                    total_duration.as_secs_f64(), audio_download_time.as_secs_f64(), video_download_time.as_secs_f64(), combine_duration.as_secs_f64());

                // Verify the combined file was created and has reasonable size
                if let Ok(metadata) = std::fs::metadata(&final_output) {
                    let combined_size_mb = metadata.len() / 1024 / 1024;
                    println!("📊 Combined file size: {} MB", combined_size_mb);

                    // Calculate performance metrics
                    let total_download_time = audio_download_time.as_secs_f64() + video_download_time.as_secs_f64();
                    let download_speed = if total_download_time > 0.0 {
                        combined_size_mb as f64 / total_download_time
                    } else {
                        0.0
                    };
                    println!("🚀 Performance: {:.1} MB/s average download speed", download_speed);
                    println!("💾 Audio caching optimization: Reusing cached audio for subsequent downloads");

                    if combined_size_mb > 5 {
                        println!("✅ Optimized download + FFmpeg combination successful - file appears complete");
                    } else {
                        println!("⚠️ Combined file seems small - please verify it plays correctly");
                    }
                } else {
                    println!("⚠️ Could not verify combined file size");
                }

                Ok(final_output)
            },
            Err(e) => {
                println!("❌ FFmpeg combination failed: {}", e);
                Err(FacebookExtractorError::FFmpegError(format!("FFmpeg combination failed: {}", e)))
            }
        }
    }

    /// Download a single stream with enhanced filename strategy
    async fn download_single_stream_with_metadata(&self, url: &str, download_dir: &str, index: usize, quality: &str, video_info: &VideoInfo, stream_type: &str) -> Result<String> {
        println!("📥 Downloading quality {} with enhanced filename: {}", index, quality);

        // Create descriptive filename using video metadata
        let filename = self.create_descriptive_filename(video_info, quality, stream_type, download_dir, index);

        // Check if file already exists
        if Path::new(&filename).exists() {
            println!("⏭️ File already exists, skipping: {}", filename);
            return Ok(filename);
        }

        // Enhanced retry logic for downloads with exponential backoff
        let max_retries = 5; // Increased retry attempts
        for attempt in 1..=max_retries {
            if attempt > 1 {
                let wait_seconds = std::cmp::min(30, 5 * attempt as u64); // Cap at 30 seconds
                println!("🔄 Retry attempt {}/{} (waiting {} seconds...)", attempt, max_retries, wait_seconds);
                tokio::time::sleep(std::time::Duration::from_secs(wait_seconds)).await;
            }

            println!("🚀 Starting download attempt {}/{}", attempt, max_retries);
            match self.try_download_single_stream(url, &filename).await {
                Ok(()) => {
                    let file_size = self.get_file_size_mb(&filename);
                    println!("✅ Download completed with descriptive filename: {} ({} MB)", filename, file_size);

                    // Verify file is not empty
                    if file_size > 0 {
                        return Ok(filename);
                    } else {
                        println!("⚠️ Downloaded file is empty, retrying...");
                        // Remove empty file and retry
                        let _ = std::fs::remove_file(&filename);
                    }
                }
                Err(e) => {
                    println!("❌ Attempt {}/{} failed: {}", attempt, max_retries, e);

                    // Clean up partial file if it exists
                    if std::path::Path::new(&filename).exists() {
                        let _ = std::fs::remove_file(&filename);
                        println!("🧹 Cleaned up partial download file");
                    }

                    if attempt == max_retries {
                        return Err(FacebookExtractorError::DownloadError(format!("Download failed after {} attempts. Last error: {}", max_retries, e)));
                    }
                }
            }
        }

        Err(FacebookExtractorError::DownloadError("Download failed after all retry attempts".to_string()))
    }

    /// Get or download cached audio stream (optimization for reusing audio across multiple video qualities)
    async fn get_or_download_cached_audio(&self, audio_url: &str, download_dir: &str) -> Result<String> {
        // Create a hash of the audio URL to use as cache key
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        audio_url.hash(&mut hasher);
        let audio_hash = hasher.finish();

        let cache_filename = format!("{}/cached_audio_{}.mp4", download_dir, audio_hash);

        // Check if audio is already cached
        if Path::new(&cache_filename).exists() {
            let file_size = self.get_file_size_mb(&cache_filename);
            if file_size > 0 {
                println!("🎵 Using cached audio: {} ({} MB)", cache_filename, file_size);
                return Ok(cache_filename);
            } else {
                // Remove empty cache file
                let _ = std::fs::remove_file(&cache_filename);
                println!("🧹 Removed empty cached audio file");
            }
        }

        // Download audio stream to cache
        println!("🎵 Downloading audio stream to cache...");
        let temp_audio_quality = format!("cached_audio_{}", audio_hash);
        match self.download_single_stream_optimized(audio_url, download_dir, 0, &temp_audio_quality, "audio").await {
            Ok(downloaded_path) => {
                // Move to cache location
                if downloaded_path != cache_filename {
                    if let Err(e) = std::fs::rename(&downloaded_path, &cache_filename) {
                        println!("⚠️ Failed to move audio to cache: {}, using original path", e);
                        return Ok(downloaded_path);
                    }
                }

                let file_size = self.get_file_size_mb(&cache_filename);
                println!("✅ Audio cached successfully: {} ({} MB)", cache_filename, file_size);
                Ok(cache_filename)
            },
            Err(e) => Err(FacebookExtractorError::DownloadError(format!("Failed to download audio for caching: {}", e)))
        }
    }

    /// Create clean, concise filename with only title and quality
    fn create_descriptive_filename(&self, video_info: &VideoInfo, quality: &str, stream_type: &str, download_dir: &str, _index: usize) -> String {
        println!("📝 Creating clean filename for: {}", quality);

        // Extract and clean the title (remove hashtags and metadata)
        let clean_title = if !video_info.title.is_empty() && video_info.title != "Unknown" {
            self.remove_hashtags_and_metadata(&video_info.title)
        } else {
            format!("Facebook_Video_{}", &video_info.video_id[..8.min(video_info.video_id.len())])
        };

        // Sanitize the cleaned title
        let sanitized_title = self.sanitize_filename(&clean_title);

        // Create quality suffix
        let quality_suffix = self.create_quality_suffix(quality, stream_type);

        // Determine base filename
        let base_filename = if !sanitized_title.is_empty() && sanitized_title.len() > 3 {
            // Use title as primary component
            format!("{}_{}", sanitized_title, quality_suffix)
        } else {
            // Fallback to video ID + quality
            println!("⚠️ Title unavailable or too short, using video ID fallback");
            format!("{}_{}", video_info.video_id, quality_suffix)
        };

        // Ensure filename length stays under 100 characters total (including .mp4)
        let max_base_length = 95; // 100 - 4 for ".mp4" - 1 for safety
        let truncated_base = if base_filename.len() > max_base_length {
            println!("✂️ Truncating filename from {} to {} characters", base_filename.len(), max_base_length);
            // Truncate title part, keep quality suffix intact
            let title_max_length = max_base_length - quality_suffix.len() - 1; // -1 for underscore
            let truncated_title = if sanitized_title.chars().count() > title_max_length {
                sanitized_title.chars().take(title_max_length).collect::<String>()
            } else {
                sanitized_title
            };
            format!("{}_{}", truncated_title, quality_suffix)
        } else {
            base_filename
        };

        // Create full path with duplicate handling
        let full_path = self.handle_duplicate_filename(download_dir, &truncated_base, "mp4");

        println!("✅ Generated clean filename: {}", full_path);
        full_path
    }

    /// Remove hashtags and unwanted metadata from title
    fn remove_hashtags_and_metadata(&self, title: &str) -> String {
        let mut clean_title = title.to_string();

        // Remove hashtags (anything starting with #)
        clean_title = regex::Regex::new(r"#\w+").unwrap()
            .replace_all(&clean_title, "")
            .to_string();

        // Remove common attribution patterns
        let attribution_patterns = vec![
            r"\|\s*By\s+[^|]*",           // "| By Author Name"
            r"By\s+[^|]*$",               // "By Author Name" at end
            r"\|\s*[^|]*Stories\s*$",     // "| Short Stories" at end
            r"Stories\s*$",               // "Stories" at end
            r"\|\s*[^|]*$",               // Any "| something" at end
        ];

        for pattern in attribution_patterns {
            if let Ok(regex) = regex::Regex::new(pattern) {
                clean_title = regex.replace_all(&clean_title, "").to_string();
            }
        }

        // Clean up multiple spaces and trim
        clean_title = clean_title
            .replace("  ", " ")
            .trim()
            .to_string();

        // If title becomes empty or too short, return original
        if clean_title.is_empty() || clean_title.len() < 3 {
            title.to_string()
        } else {
            clean_title
        }
    }

    /// Sanitize filename to prevent filesystem issues
    fn sanitize_filename(&self, title: &str) -> String {
        // Safe Unicode-aware truncation for display
        let display_title = if title.chars().count() > 50 {
            title.chars().take(50).collect::<String>()
        } else {
            title.to_string()
        };
        println!("🧹 Sanitizing filename: {}", display_title);

        // Remove or replace invalid characters: < > : " | ? * \ /
        let mut sanitized = title
            .replace(['<', '>', ':', '"', '|', '?', '*', '\\', '/'], "_")
            .replace("  ", " ") // Replace double spaces with single space
            .replace(' ', "_") // Replace spaces with underscores
            .replace("__", "_"); // Replace double underscores with single

        // Remove control characters and other problematic characters
        sanitized = sanitized.chars()
            .filter(|c| !c.is_control() && *c != '\u{FEFF}') // Remove control chars and BOM
            .collect();

        // Handle Unicode characters - keep them but ensure they're valid
        sanitized = sanitized
            .chars()
            .map(|c| {
                match c {
                    // Replace some common problematic Unicode chars
                    '\u{2013}' | '\u{2014}' => '-', // En dash, Em dash
                    '\u{2018}' | '\u{2019}' => '\'', // Left/right single quotation marks
                    '\u{201C}' | '\u{201D}' => '"', // Left/right double quotation marks
                    '\u{2026}' => '_', // Horizontal ellipsis
                    _ => c
                }
            })
            .collect();

        // Remove leading/trailing dots, spaces, and underscores
        sanitized = sanitized.trim_matches(['.', ' ', '_']).to_string();

        // Ensure we don't have reserved Windows filenames
        let reserved_names = ["CON", "PRN", "AUX", "NUL", "COM1", "COM2", "COM3", "COM4",
                             "COM5", "COM6", "COM7", "COM8", "COM9", "LPT1", "LPT2",
                             "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9"];

        if reserved_names.contains(&sanitized.to_uppercase().as_str()) {
            sanitized = format!("{}_video", sanitized);
        }

        // Final cleanup - ensure it's not empty
        if sanitized.is_empty() {
            sanitized = "video".to_string();
        }

        // Safe Unicode-aware truncation for display
        let display_result = if sanitized.chars().count() > 50 {
            sanitized.chars().take(50).collect::<String>()
        } else {
            sanitized.clone()
        };
        println!("✅ Sanitized result: {}", display_result);
        sanitized
    }

    /// Create clean, minimal quality suffix for filename (resolution only)
    fn create_quality_suffix(&self, quality: &str, _stream_type: &str) -> String {
        // Extract ONLY the resolution number (e.g., "1080", "720", "540", "360")
        let mut clean_quality = quality
            .replace(" ", "")
            .replace("(", "")
            .replace(")", "")
            .replace("/", "")
            .replace("Full", "")
            .replace("HD", "")
            .replace("Video+Audio", "")
            .replace("Combined", "")
            .replace("complete", "")
            .replace("video", "")
            .replace("audio", "")
            .replace("_", "")
            .replace("-", "")
            .replace("+", "")
            .replace("hd", "")
            .replace("fullhd", "")
            .replace("combined", "")
            .to_lowercase();

        // Clean up any remaining artifacts and extract just the number
        clean_quality = clean_quality.trim().to_string();

        // Extract just the resolution number using regex-like approach
        let resolution_number = clean_quality
            .chars()
            .filter(|c| c.is_ascii_digit())
            .collect::<String>();

        // Convert to standard resolution format
        match resolution_number.as_str() {
            "1080" => "1080p".to_string(),
            "720" => "720p".to_string(),
            "540" => "540p".to_string(),
            "480" => "480p".to_string(),
            "360" => "360p".to_string(),
            "240" => "240p".to_string(),
            "144" => "144p".to_string(),
            _ => {
                // If we can't extract a standard resolution, try to find 'p' suffix
                if clean_quality.contains('p') {
                    // Extract everything before 'p' and add 'p' back
                    let parts: Vec<&str> = clean_quality.split('p').collect();
                    if let Some(first_part) = parts.first() {
                        let numbers: String = first_part.chars().filter(|c| c.is_ascii_digit()).collect();
                        if !numbers.is_empty() {
                            return format!("{}p", numbers);
                        }
                    }
                }
                // Default fallback
                "360p".to_string()
            }
        }
    }

    /// Handle duplicate filenames by adding incremental numbers
    fn handle_duplicate_filename(&self, download_dir: &str, base_name: &str, extension: &str) -> String {
        let mut counter = 1;
        let mut filename = format!("{}/{}.{}", download_dir, base_name, extension);

        // Check if file exists and increment counter until we find a unique name
        while Path::new(&filename).exists() {
            counter += 1;
            filename = format!("{}/{}_{}.{}", download_dir, base_name, counter, extension);

            // Safety check to prevent infinite loop
            if counter > 999 {
                println!("⚠️ Too many duplicate files, using timestamp suffix");
                let timestamp = std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap_or_default()
                    .as_secs();
                filename = format!("{}/{}_{}.{}", download_dir, base_name, timestamp, extension);
                break;
            }
        }

        if counter > 1 {
            println!("🔄 Duplicate filename detected, using: {}", filename);
        }

        filename
    }

    /// Download a single stream to local file with retry logic
    async fn download_single_stream(&self, url: &str, download_dir: &str, index: usize, quality: &str) -> Result<String> {
        println!("📥 Downloading quality {}: {}", index, quality);

        // Create a safe filename
        let safe_quality = quality
            .replace(" ", "_")
            .replace("(", "")
            .replace(")", "")
            .replace("/", "_")
            .to_lowercase();

        let filename = format!("{}/video_{}_{}.mp4", download_dir, index, safe_quality);

        // Check if file already exists
        if Path::new(&filename).exists() {
            println!("⏭️ File already exists, skipping: {}", filename);
            return Ok(filename);
        }

        // Enhanced retry logic for downloads with exponential backoff
        let max_retries = 5; // Increased retry attempts
        for attempt in 1..=max_retries {
            if attempt > 1 {
                let wait_seconds = std::cmp::min(30, 5 * attempt as u64); // Cap at 30 seconds
                println!("🔄 Retry attempt {}/{} (waiting {} seconds...)", attempt, max_retries, wait_seconds);
                tokio::time::sleep(std::time::Duration::from_secs(wait_seconds)).await;
            }

            println!("🚀 Starting download attempt {}/{}", attempt, max_retries);
            match self.try_download_single_stream(url, &filename).await {
                Ok(()) => {
                    let file_size = self.get_file_size_mb(&filename);
                    println!("✅ Download completed: {} ({} MB)", filename, file_size);

                    // Verify file is not empty
                    if file_size > 0 {
                        return Ok(filename);
                    } else {
                        println!("⚠️ Downloaded file is empty, retrying...");
                        // Remove empty file and retry
                        let _ = std::fs::remove_file(&filename);
                    }
                }
                Err(e) => {
                    println!("❌ Attempt {}/{} failed: {}", attempt, max_retries, e);

                    // Clean up partial file if it exists
                    if std::path::Path::new(&filename).exists() {
                        let _ = std::fs::remove_file(&filename);
                        println!("🧹 Cleaned up partial download file");
                    }

                    if attempt == max_retries {
                        return Err(FacebookExtractorError::DownloadError(format!("Download failed after {} attempts. Last error: {}", max_retries, e)));
                    }
                }
            }
        }

        Err(FacebookExtractorError::DownloadError("Download failed after all retry attempts".to_string()))
    }

    /// Try to download a single stream (internal function)
    async fn try_download_single_stream(&self, url: &str, filename: &str) -> Result<()> {

        println!("🌐 Fetching video from: {}", &url[..80.min(url.len())]);
        println!("🔍 Full URL: {}", url);

        // Analyze URL structure for debugging
        println!("🔍 URL Analysis:");
        if url.contains("video.fsgn19-1.fna.fbcdn.net") {
            println!("   ✅ Valid Facebook CDN domain");
        } else {
            println!("   ⚠️ Unexpected domain");
        }

        if url.contains("efg=") {
            println!("   ✅ Contains EFG metadata");
        } else {
            println!("   ⚠️ Missing EFG metadata");
        }

        if url.contains("_nc_ohc=") {
            println!("   ✅ Contains authentication hash");
        } else {
            println!("   ⚠️ Missing authentication hash");
        }

        if url.contains("ccb=") {
            println!("   ✅ Contains cache control");
        } else {
            println!("   ⚠️ Missing cache control");
        }

        println!("🔧 Request headers:");
        println!("   User-Agent: {}", self.primary_user_agent());
        println!("   Referer: https://www.facebook.com/");
        println!("   Range: bytes=0-");

        // Download the video with extended timeout for large files
        let response = tokio::time::timeout(
            std::time::Duration::from_secs(INITIAL_REQUEST_TIMEOUT_SECS), // 5 minute timeout for initial request
            self.client
                .get(url)
                .header("User-Agent", self.primary_user_agent())
                .header("Accept", "*/*")
                .header("Accept-Language", "en-US,en;q=0.5")
                .header("Referer", "https://www.facebook.com/")
                .header("Origin", "https://www.facebook.com")
                .header("Connection", "keep-alive") // Keep connection alive
                .header("Range", "bytes=0-") // Support partial downloads
                .send()
        ).await
        .map_err(|_| FacebookExtractorError::TimeoutError("Initial request timeout after 5 minutes".to_string()))?
        .map_err(|e| FacebookExtractorError::NetworkError(e))?;

        println!("📊 Response status: {}", response.status());
        println!("📋 Response headers:");
        for (name, value) in response.headers() {
            if let Ok(value_str) = value.to_str() {
                println!("   {}: \"{}\"", name, value_str);
            }
        }

        if !response.status().is_success() && response.status().as_u16() != 206 {
            let status = response.status();
            let error_body = response.text().await.unwrap_or_else(|_| "Unable to read error body".to_string());
            println!("❌ Error response body: {}", &error_body[..std::cmp::min(error_body.len(), 1000)]);
            return Err(FacebookExtractorError::DownloadError(format!("Download failed with status: {}", status)));
        }

        // Get content length for progress tracking
        let total_size = response.content_length().unwrap_or(0);
        println!("📊 File size: {} MB", total_size / 1024 / 1024);

        // Stream the download to file
        let mut file = tokio::fs::File::create(&filename).await
            .map_err(|e| FacebookExtractorError::FileSystemError(e))?;

        let mut downloaded = 0u64;
        let mut stream = response.bytes_stream();

        use tokio_stream::StreamExt;
        use tokio::io::AsyncWriteExt;

        let start_time = std::time::Instant::now();
        let mut consecutive_timeouts = 0;
        const MAX_CONSECUTIVE_TIMEOUTS: usize = 3;

        loop {
            match tokio::time::timeout(
                std::time::Duration::from_secs(DOWNLOAD_CHUNK_TIMEOUT_SECS), // 2 minute timeout per chunk (generous for slow connections)
                stream.next()
            ).await {
                Ok(Some(chunk_result)) => {
                    let chunk = chunk_result.map_err(|e| FacebookExtractorError::NetworkError(e))?;

                    file.write_all(&chunk).await
                        .map_err(|e| FacebookExtractorError::FileSystemError(e))?;

                    downloaded += chunk.len() as u64;
                    consecutive_timeouts = 0; // Reset timeout counter on successful chunk

                    // Show progress every 1MB
                    if downloaded % PROGRESS_UPDATE_INTERVAL_BYTES == 0 || downloaded == total_size {
                        let progress = if total_size > 0 {
                            (downloaded as f64 / total_size as f64 * 100.0) as u32
                        } else {
                            0
                        };
                        let elapsed = start_time.elapsed().as_secs_f64();
                        let speed_mbps = if elapsed > 0.0 {
                            (downloaded as f64 / 1024.0 / 1024.0) / elapsed
                        } else {
                            0.0
                        };
                        println!("📈 Progress: {} MB / {} MB ({}%) - Speed: {:.1} MB/s",
                            downloaded / 1024 / 1024,
                            total_size / 1024 / 1024,
                            progress,
                            speed_mbps
                        );
                    }
                }
                Ok(None) => break, // Stream ended
                Err(_) => {
                    consecutive_timeouts += 1;
                    println!("⚠️ Chunk timeout ({}/{}), continuing...", consecutive_timeouts, MAX_CONSECUTIVE_TIMEOUTS);

                    if consecutive_timeouts >= MAX_CONSECUTIVE_TIMEOUTS {
                        return Err(FacebookExtractorError::TimeoutError(format!("Download failed: {} consecutive timeouts (no data for {} minutes)",
                            MAX_CONSECUTIVE_TIMEOUTS,
                            MAX_CONSECUTIVE_TIMEOUTS * 2)));
                    }
                    // Continue the loop to try next chunk
                }
            }
        }

        file.flush().await
            .map_err(|e| FacebookExtractorError::FileSystemError(e))?;

        println!("✅ Download completed: {} ({} MB)", filename, downloaded / 1024 / 1024);
        Ok(())
    }

    /// Get file size in MB
    fn get_file_size_mb(&self, filename: &str) -> u64 {
        std::fs::metadata(filename)
            .map(|metadata| metadata.len() / 1024 / 1024)
            .unwrap_or(0)
    }

    /// Optimized download function for parallel stream processing
    async fn download_single_stream_optimized(&self, url: &str, download_dir: &str, index: usize, quality: &str, stream_type: &str) -> Result<String> {
        println!("🚀 [{}] Starting optimized download: {}", stream_type.to_uppercase(), quality);

        // Create a safe filename
        let safe_quality = quality
            .replace(" ", "_")
            .replace("(", "")
            .replace(")", "")
            .replace("/", "_")
            .to_lowercase();

        let filename = format!("{}/video_{}_{}.mp4", download_dir, index, safe_quality);

        // Check if file already exists
        if Path::new(&filename).exists() {
            println!("⏭️ [{}] File already exists, skipping: {}", stream_type.to_uppercase(), filename);
            return Ok(filename);
        }

        // Enhanced retry logic with performance optimization
        let max_retries = 3; // Reduced retries for parallel downloads
        for attempt in 1..=max_retries {
            if attempt > 1 {
                let wait_seconds = 2 * attempt as u64; // Shorter waits for parallel processing
                println!("🔄 [{}] Retry attempt {}/{} (waiting {} seconds...)", stream_type.to_uppercase(), attempt, max_retries, wait_seconds);
                tokio::time::sleep(std::time::Duration::from_secs(wait_seconds)).await;
            }

            println!("⚡ [{}] Starting download attempt {}/{}", stream_type.to_uppercase(), attempt, max_retries);
            match self.try_download_single_stream_optimized(url, &filename, stream_type).await {
                Ok(()) => {
                    let file_size = self.get_file_size_mb(&filename);
                    println!("✅ [{}] Download completed: {} ({} MB)", stream_type.to_uppercase(), filename, file_size);

                    // Verify file is not empty
                    if file_size > 0 {
                        return Ok(filename);
                    } else {
                        println!("⚠️ [{}] Downloaded file is empty, retrying...", stream_type.to_uppercase());
                        // Remove empty file and retry
                        let _ = std::fs::remove_file(&filename);
                    }
                }
                Err(e) => {
                    println!("❌ [{}] Attempt {}/{} failed: {}", stream_type.to_uppercase(), attempt, max_retries, e);

                    // Clean up partial file if it exists
                    if std::path::Path::new(&filename).exists() {
                        let _ = std::fs::remove_file(&filename);
                        println!("🧹 [{}] Cleaned up partial download file", stream_type.to_uppercase());
                    }

                    if attempt == max_retries {
                        return Err(FacebookExtractorError::DownloadError(format!("[{}] Download failed after {} attempts. Last error: {}", stream_type.to_uppercase(), max_retries, e)));
                    }
                }
            }
        }

        Err(FacebookExtractorError::DownloadError(format!("[{}] Download failed after all retry attempts", stream_type.to_uppercase())))
    }

    /// Multi-threaded optimized download implementation with concurrent chunk processing
    async fn try_download_single_stream_optimized(&self, url: &str, filename: &str, stream_type: &str) -> Result<()> {
        println!("🌐 [{}] Fetching from: {}", stream_type.to_uppercase(), &url[..80.min(url.len())]);

        // First, get file size with HEAD request for multi-threaded chunking
        let head_response = tokio::time::timeout(
            std::time::Duration::from_secs(HEAD_REQUEST_TIMEOUT_SECS),
            self.client
                .head(url)
                .header("User-Agent", self.primary_user_agent())
                .header("Referer", "https://www.facebook.com/")
                .send()
        ).await
        .map_err(|_| FacebookExtractorError::TimeoutError(format!("[{}] HEAD request timeout", stream_type.to_uppercase())))?
        .map_err(|e| FacebookExtractorError::NetworkError(e))?;

        let total_size = head_response.content_length().unwrap_or(0);
        println!("📊 [{}] File size: {} MB", stream_type.to_uppercase(), total_size / 1024 / 1024);

        // Check if server supports range requests
        let supports_ranges = head_response.headers()
            .get("accept-ranges")
            .and_then(|v| v.to_str().ok())
            .map(|v| v == "bytes")
            .unwrap_or(false);

        if supports_ranges && total_size > MULTITHREADING_MIN_SIZE_BYTES { // Use multi-threading for files > 5MB
            self.download_with_multithreading(url, filename, stream_type, total_size).await
        } else {
            self.download_single_threaded(url, filename, stream_type).await
        }
    }

    /// Multi-threaded download with concurrent chunks
    async fn download_with_multithreading(&self, url: &str, filename: &str, stream_type: &str, total_size: u64) -> Result<()> {
        println!("🚀 [{}] Using multi-threaded download with concurrent chunks", stream_type.to_uppercase());

        // Calculate optimal chunk size and thread count
        let chunk_size = std::cmp::max(1024 * 1024, total_size / 8); // At least 1MB per chunk, max 8 chunks
        let num_chunks = ((total_size + chunk_size - 1) / chunk_size) as usize;
        let actual_num_chunks = std::cmp::min(num_chunks, 6); // Limit to 6 concurrent connections

        println!("📊 [{}] Downloading {} MB in {} chunks of ~{} MB each",
            stream_type.to_uppercase(),
            total_size / 1024 / 1024,
            actual_num_chunks,
            chunk_size / 1024 / 1024
        );

        // Create temporary files for each chunk
        let mut chunk_files = Vec::new();
        let mut chunk_tasks = Vec::new();

        let start_time = std::time::Instant::now();

        // Spawn concurrent download tasks for each chunk
        for i in 0..actual_num_chunks {
            let start_byte = i as u64 * chunk_size;
            let end_byte = if i == actual_num_chunks - 1 {
                total_size - 1
            } else {
                (i as u64 + 1) * chunk_size - 1
            };

            let chunk_filename = format!("{}.chunk{}", filename, i);
            chunk_files.push(chunk_filename.clone());

            let client = self.client.clone();
            let url = url.to_string();
            let user_agent = self.primary_user_agent().to_string();
            let stream_type = stream_type.to_string();

            let task = tokio::spawn(async move {
                Self::download_chunk(client, url, chunk_filename, start_byte, end_byte, i, user_agent, stream_type).await
            });

            chunk_tasks.push(task);
        }

        // Wait for all chunks to complete
        let mut chunk_results = Vec::new();
        for (i, task) in chunk_tasks.into_iter().enumerate() {
            match task.await {
                Ok(result) => {
                    match result {
                        Ok(bytes_downloaded) => {
                            println!("✅ [{}] Chunk {} completed: {} MB",
                                stream_type.to_uppercase(), i + 1, bytes_downloaded / 1024 / 1024);
                            chunk_results.push(Ok(bytes_downloaded));
                        }
                        Err(e) => {
                            println!("❌ [{}] Chunk {} failed: {}", stream_type.to_uppercase(), i + 1, e);
                            chunk_results.push(Err(e));
                        }
                    }
                }
                Err(e) => {
                    println!("❌ [{}] Chunk {} task failed: {}", stream_type.to_uppercase(), i + 1, e);
                    chunk_results.push(Err(format!("Task join error: {}", e).into()));
                }
            }
        }

        // Check if all chunks succeeded
        let mut total_downloaded = 0u64;
        for result in &chunk_results {
            match result {
                Ok(bytes) => total_downloaded += bytes,
                Err(e) => {
                    // Clean up chunk files on error
                    for chunk_file in &chunk_files {
                        let _ = tokio::fs::remove_file(chunk_file).await;
                    }
                    return Err(FacebookExtractorError::DownloadError(format!("[{}] Multi-threaded download failed: {}", stream_type.to_uppercase(), e)));
                }
            }
        }

        // Combine all chunks into final file
        println!("🔧 [{}] Combining {} chunks into final file...", stream_type.to_uppercase(), actual_num_chunks);
        let combine_start = std::time::Instant::now();

        let mut final_file = tokio::fs::File::create(filename).await
            .map_err(|e| FacebookExtractorError::FileSystemError(e))?;

        use tokio::io::AsyncWriteExt;

        for (_i, chunk_file) in chunk_files.iter().enumerate() {
            let chunk_data = tokio::fs::read(chunk_file).await
                .map_err(|e| FacebookExtractorError::FileSystemError(e))?;

            final_file.write_all(&chunk_data).await
                .map_err(|e| FacebookExtractorError::FileSystemError(e))?;

            // Clean up chunk file
            let _ = tokio::fs::remove_file(chunk_file).await;
        }

        final_file.flush().await
            .map_err(|e| FacebookExtractorError::FileSystemError(e))?;

        let total_time = start_time.elapsed().as_secs_f64();
        let combine_time = combine_start.elapsed().as_secs_f64();
        let download_time = total_time - combine_time;
        let avg_speed = if download_time > 0.0 {
            (total_downloaded as f64 / 1024.0 / 1024.0) / download_time
        } else {
            0.0
        };

        println!("✅ [{}] Multi-threaded download completed: {} ({} MB) in {:.2}s (download: {:.2}s + combine: {:.2}s) - Avg Speed: {:.1} MB/s",
            stream_type.to_uppercase(), filename, total_downloaded / 1024 / 1024, total_time, download_time, combine_time, avg_speed);

        Ok(())
    }

    /// Download a single chunk with range request
    async fn download_chunk(
        client: reqwest::Client,
        url: String,
        filename: String,
        start_byte: u64,
        end_byte: u64,
        chunk_id: usize,
        user_agent: String,
        stream_type: String,
    ) -> std::result::Result<u64, String> {
        let range_header = format!("bytes={}-{}", start_byte, end_byte);

        let response = tokio::time::timeout(
            std::time::Duration::from_secs(120), // 2 minute timeout per chunk
            client
                .get(&url)
                .header("User-Agent", user_agent)
                .header("Accept", "*/*")
                .header("Accept-Encoding", "gzip, deflate, br")
                .header("Referer", "https://www.facebook.com/")
                .header("Origin", "https://www.facebook.com")
                .header("Connection", "keep-alive")
                .header("Range", range_header)
                .send()
        ).await
        .map_err(|_| format!("[{}] Chunk {} timeout", stream_type.to_uppercase(), chunk_id + 1))?
        .map_err(|e| format!("[{}] Chunk {} request failed: {}", stream_type.to_uppercase(), chunk_id + 1, e))?;

        if !response.status().is_success() && response.status().as_u16() != 206 {
            return Err(format!("[{}] Chunk {} failed with status: {}", stream_type.to_uppercase(), chunk_id + 1, response.status()));
        }

        // Stream chunk to file
        let mut file = tokio::fs::File::create(&filename).await
            .map_err(|e| format!("[{}] Chunk {} failed to create file: {}", stream_type.to_uppercase(), chunk_id + 1, e))?;

        let mut downloaded = 0u64;
        let mut stream = response.bytes_stream();

        use tokio_stream::StreamExt;
        use tokio::io::AsyncWriteExt;

        while let Some(chunk_result) = stream.next().await {
            let chunk = chunk_result
                .map_err(|e| format!("[{}] Chunk {} stream error: {}", stream_type.to_uppercase(), chunk_id + 1, e))?;

            file.write_all(&chunk).await
                .map_err(|e| format!("[{}] Chunk {} write error: {}", stream_type.to_uppercase(), chunk_id + 1, e))?;

            downloaded += chunk.len() as u64;
        }

        file.flush().await
            .map_err(|e| format!("[{}] Chunk {} flush error: {}", stream_type.to_uppercase(), chunk_id + 1, e))?;

        Ok(downloaded)
    }

    /// Single-threaded download fallback
    async fn download_single_threaded(&self, url: &str, filename: &str, stream_type: &str) -> Result<()> {
        println!("📡 [{}] Using single-threaded download", stream_type.to_uppercase());

        let response = tokio::time::timeout(
            std::time::Duration::from_secs(SINGLE_THREADED_TIMEOUT_SECS),
            self.client
                .get(url)
                .header("User-Agent", self.primary_user_agent())
                .header("Accept", "*/*")
                .header("Accept-Language", "en-US,en;q=0.5")
                .header("Accept-Encoding", "gzip, deflate, br")
                .header("Referer", "https://www.facebook.com/")
                .header("Origin", "https://www.facebook.com")
                .header("Connection", "keep-alive")
                .send()
        ).await
        .map_err(|_| FacebookExtractorError::TimeoutError(format!("[{}] Request timeout", stream_type.to_uppercase())))?
        .map_err(|e| FacebookExtractorError::NetworkError(e))?;

        if !response.status().is_success() {
            return Err(FacebookExtractorError::DownloadError(format!("[{}] Download failed with status: {}", stream_type.to_uppercase(), response.status())));
        }

        let total_size = response.content_length().unwrap_or(0);
        let mut file = tokio::fs::File::create(filename).await
            .map_err(|e| FacebookExtractorError::FileSystemError(e))?;

        let mut downloaded = 0u64;
        let mut stream = response.bytes_stream();
        let start_time = std::time::Instant::now();

        use tokio_stream::StreamExt;
        use tokio::io::AsyncWriteExt;

        while let Some(chunk_result) = stream.next().await {
            let chunk = chunk_result
                .map_err(|e| FacebookExtractorError::NetworkError(e))?;

            file.write_all(&chunk).await
                .map_err(|e| FacebookExtractorError::FileSystemError(e))?;

            downloaded += chunk.len() as u64;

            // Progress every 2MB
            if downloaded % (2 * 1024 * 1024) == 0 || downloaded == total_size {
                let progress = if total_size > 0 {
                    (downloaded as f64 / total_size as f64 * 100.0) as u32
                } else {
                    0
                };
                let elapsed = start_time.elapsed().as_secs_f64();
                let speed = if elapsed > 0.0 {
                    (downloaded as f64 / 1024.0 / 1024.0) / elapsed
                } else {
                    0.0
                };
                println!("📈 [{}] Progress: {} MB / {} MB ({}%) - Speed: {:.1} MB/s",
                    stream_type.to_uppercase(),
                    downloaded / 1024 / 1024,
                    total_size / 1024 / 1024,
                    progress,
                    speed
                );
            }
        }

        file.flush().await
            .map_err(|e| FacebookExtractorError::FileSystemError(e))?;

        let total_time = start_time.elapsed().as_secs_f64();
        let avg_speed = if total_time > 0.0 {
            (downloaded as f64 / 1024.0 / 1024.0) / total_time
        } else {
            0.0
        };

        println!("✅ [{}] Single-threaded download completed: {} ({} MB) in {:.2}s - Avg Speed: {:.1} MB/s",
            stream_type.to_uppercase(), filename, downloaded / 1024 / 1024, total_time, avg_speed);

        Ok(())
    }

    /// Combine video and audio streams using FFmpeg
    async fn combine_streams_with_ffmpeg(&self, video_path: &str, audio_path: &str, output_path: &str) -> Result<()> {
        // Check if FFmpeg is available
        let ffmpeg_check = std::process::Command::new("ffmpeg")
            .arg("-version")
            .output();

        if ffmpeg_check.is_err() {
            return Err(FacebookExtractorError::FFmpegError("FFmpeg not found. Please install FFmpeg to combine DASH streams.".to_string()));
        }

        println!("🔧 Running FFmpeg to combine streams...");
        println!("   Video: {}", video_path);
        println!("   Audio: {}", audio_path);
        println!("   Output: {}", output_path);

        // Run FFmpeg command to combine streams
        let output = std::process::Command::new("ffmpeg")
            .arg("-i").arg(video_path)
            .arg("-i").arg(audio_path)
            .arg("-c:v").arg("copy")        // Copy video codec (no re-encoding)
            .arg("-c:a").arg("copy")        // Copy audio codec (no re-encoding)
            .arg("-shortest")               // Match shortest stream duration
            .arg("-y")                      // Overwrite output file
            .arg(output_path)
            .output()
            .map_err(|e| FacebookExtractorError::FFmpegError(format!("Failed to run FFmpeg: {}", e)))?;

        if output.status.success() {
            println!("✅ FFmpeg combination successful");
            Ok(())
        } else {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            Err(FacebookExtractorError::FFmpegError(format!("FFmpeg failed: {}", error_msg)))
        }
    }

    /// Clean Facebook URL by removing HTML entities and malformed parts
    fn clean_facebook_url(&self, url: &str) -> String {
        let mut clean_url = url.to_string();

        // First, remove any DASH manifest XML contamination
        if let Some(xml_start) = clean_url.find("u003C") {
            clean_url = clean_url[..xml_start].to_string();
        }

        // Remove any pipe-separated URLs (combined stream artifacts)
        if let Some(pipe_pos) = clean_url.find("|https://") {
            clean_url = clean_url[..pipe_pos].to_string();
        }

        // Replace HTML entities
        clean_url = clean_url.replace("&amp;", "&");
        clean_url = clean_url.replace("&quot;", "\"");
        clean_url = clean_url.replace("&#039;", "'");
        clean_url = clean_url.replace("&lt;", "<");
        clean_url = clean_url.replace("&gt;", ">");

        // Remove Unicode escape sequences
        clean_url = clean_url.replace("\\u0026", "&");
        clean_url = clean_url.replace("\\u003C", "<");
        clean_url = clean_url.replace("\\u003E", ">");
        clean_url = clean_url.replace("\\u0022", "\"");
        clean_url = clean_url.replace("\\u0027", "'");

        // Remove escaped slashes
        clean_url = clean_url.replace("\\/", "/");
        clean_url = clean_url.replace("\\\\", "\\");

        // Remove any trailing garbage after .mp4
        if let Some(mp4_pos) = clean_url.find(".mp4") {
            let end_pos = mp4_pos + 4;
            // Look for query parameters that are valid
            if let Some(query_start) = clean_url[end_pos..].find('?') {
                let query_part = &clean_url[end_pos + query_start..];
                // Keep valid query parameters
                if query_part.contains("efg=") || query_part.contains("_nc_") || query_part.contains("oh=") {
                    // Keep the URL as is
                } else {
                    // Truncate at .mp4
                    clean_url = clean_url[..end_pos].to_string();
                }
            } else {
                // Look for any non-URL characters after .mp4
                let after_mp4 = &clean_url[end_pos..];
                if let Some(bad_char_pos) = after_mp4.find(|c: char| !c.is_ascii_alphanumeric() && !"?&=_-".contains(c)) {
                    clean_url = clean_url[..end_pos + bad_char_pos].to_string();
                }
            }
        }

        // Remove any leading/trailing quotes or whitespace
        clean_url = clean_url.trim_matches('"').trim().to_string();

        // If URL doesn't start with https://, try to fix it
        if !clean_url.starts_with("https://") && clean_url.contains("https://") {
            if let Some(https_pos) = clean_url.find("https://") {
                clean_url = clean_url[https_pos..].to_string();
            }
        }

        clean_url
    }
}

#[tokio::main]
async fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    let args: Vec<String> = env::args().collect();

    // Check for test mode
    if args.len() > 1 && args[1] == "--test-filenames" {
        test_filename_generation()?;
        test_edge_cases()?;
        return Ok(());
    }

    let test_url = if args.len() > 1 {
        &args[1]
    } else {
        "https://www.facebook.com/yeah1stories/videos/1045988757255181"
    };

    println!("🚀 Facebook Video Extractor CLI Test");
    println!("=====================================");
    println!("Testing URL: {}", test_url);
    println!();

    let extractor = FacebookExtractorCLI::new()?;

    match extractor.extract_video_info(test_url).await {
        Ok(video_info) => {
            println!("\n🎉 SUCCESS! Video information extracted:");
            println!("=====================================");

            // Basic video info
            println!("📝 Title: {}", video_info.title);
            println!("🆔 Video ID: {}", video_info.video_id);
            println!("⏱️ Duration: {}", video_info.duration);
            if !video_info.thumbnail.is_empty() {
                println!("🖼️ Thumbnail: {}", video_info.thumbnail);
            }

            // Metadata
            println!("\n📊 Video Metadata:");
            println!("👤 Author: {}", video_info.metadata.author);
            println!("📅 Published: {}", video_info.metadata.publish_date);
            println!("👁️ Views: {}", video_info.metadata.views);
            println!("👍 Likes: {}", video_info.metadata.likes);
            println!("💬 Comments: {}", video_info.metadata.comments);
            println!("🔄 Shares: {}", video_info.metadata.shares);

            if !video_info.metadata.hashtags.is_empty() {
                println!("🏷️ Hashtags: {}", video_info.metadata.hashtags.join(", "));
            }

            if !video_info.metadata.description.is_empty() && video_info.metadata.description != "No description available" {
                println!("\n📝 Description:");
                println!("{}", video_info.metadata.description);
            }

            // Video qualities (complete streams only)
            if video_info.qualities.is_empty() {
                println!("\n⚠️ No complete video+audio streams available");
                println!("   Only audio-only or video-only streams were found");
            } else {
                println!("\n🎬 Complete Video+Audio Streams Available:");
                for (i, quality) in video_info.qualities.iter().enumerate() {
                    println!("  {}. {} ({}x{}) - {} - Complete Video+Audio",
                        i + 1,
                        quality.quality,
                        quality.width,
                        quality.height,
                        quality.size
                    );
                    println!("     Stream Type: {:?}", quality.stream_type);
                    println!("     URL: {}", &quality.download_url[..80.min(quality.download_url.len())]);
                }
            }

            // Ask user if they want to download
            println!("\n💾 Do you want to download videos? (y/n)");
            let mut input = String::new();
            std::io::stdin().read_line(&mut input).expect("Failed to read input");

            if input.trim().to_lowercase() == "y" || input.trim().to_lowercase() == "yes" {
                println!("\n🚀 Starting downloads...");

                // Create downloads directory
                let download_dir = format!("downloads/{}", video_info.video_id);
                if let Err(e) = fs::create_dir_all(&download_dir) {
                    println!("❌ Failed to create download directory: {}", e);
                    std::process::exit(1);
                }

                // Enhanced download logic for complete streams only
                if video_info.qualities.is_empty() {
                    println!("❌ No complete video+audio streams found for download!");
                    println!("   This video may only have audio-only or video-only streams,");
                    println!("   or the streams may be protected/expired.");
                } else {
                    // Show summary of available complete streams
                    let quality_summary: Vec<String> = video_info.qualities.iter()
                        .map(|q| format!("{} ({})", q.quality, q.size))
                        .collect();

                    println!("\n🎬 Found {} complete video+audio streams:", video_info.qualities.len());
                    println!("📋 Available qualities: {}", quality_summary.join(", "));

                    // Download each complete stream
                    for (i, quality) in video_info.qualities.iter().enumerate() {
                        println!("\n🔍 Processing stream {}/{}: {}", i + 1, video_info.qualities.len(), quality.quality);

                        // Show URL info (but don't clean combined URLs yet)
                        let display_url = if quality.download_url.starts_with("COMBINED:") {
                            "COMBINED stream (video + audio)"
                        } else {
                            &quality.download_url[..100.min(quality.download_url.len())]
                        };
                        println!("🔗 URL: {}", display_url);

                        // Create quality-specific filename
                        let quality_label = format!("{}_complete_video_audio",
                            quality.quality.to_lowercase().replace(" ", "_").replace("(", "").replace(")", ""));

                        // Use enhanced filename strategy with video metadata
                        match extractor.download_video_with_metadata(&quality.download_url, &download_dir, i + 1, &quality_label, &video_info).await {
                            Ok(filename) => {
                                println!("✅ Downloaded: {}", filename);

                                // Enhanced file verification
                                if let Ok(metadata) = std::fs::metadata(&filename) {
                                    let actual_size_mb = metadata.len() / 1024 / 1024;
                                    let expected_size_mb = quality.estimated_size_mb;

                                    println!("📊 File verification:");
                                    println!("   📁 Actual size: {} MB", actual_size_mb);
                                    println!("   📈 Expected size: {} MB", expected_size_mb);

                                    if actual_size_mb >= 5 {
                                        let size_ratio = actual_size_mb as f32 / expected_size_mb as f32;
                                        if size_ratio >= 0.5 && size_ratio <= 2.0 {
                                            println!("   ✅ Size verification: PASSED (complete video+audio)");
                                        } else {
                                            println!("   ⚠️ Size verification: WARNING (unexpected size)");
                                        }
                                    } else {
                                        println!("   ❌ Size verification: FAILED (file too small, likely incomplete)");
                                    }
                                }
                            },
                            Err(e) => {
                                println!("❌ Failed to download {}: {}", quality.quality, e);
                                if e.contains("403") {
                                    println!("   💡 This stream URL may have expired or requires authentication");
                                }
                            }
                        }
                    }
                }

                println!("\n🎉 Download process completed!");
                println!("📁 Files saved to: {}", download_dir);
            } else {
                println!("\n👋 Skipping downloads. Goodbye!");
            }
        },
        Err(error) => {
            println!("\n❌ FAILED: {}", error);
            std::process::exit(1);
        }
    }

    Ok(())
}

/// Test function to demonstrate enhanced filename strategy
#[allow(dead_code)]
fn test_filename_generation() -> std::result::Result<(), Box<dyn std::error::Error>> {
    println!("\n🧪 Testing Enhanced Filename Strategy");
    println!("=====================================");

    let extractor = FacebookExtractorCLI::new()?;

    // Test cases with various edge cases including hashtag removal
    let test_cases = vec![
        ("Amazing Sunset Timelapse - 4K Ultra HD", "720p HD", "combined"),
        ("Video with Special Characters: <test> \"quotes\" |pipe| ?question", "1080p Full HD", ""),
        ("Very Long Title That Exceeds Normal Length Limits And Should Be Truncated Properly To Prevent Filesystem Issues While Maintaining Readability And Useful Information For Users", "540p", "video"),
        ("", "360p", "audio"), // Empty title test
        ("CON", "480p", ""), // Reserved Windows filename test
        ("Video with Unicode: 你好世界 🎬 Émojis & Spëcial Chars", "720p", "combined"),
        ("Multiple    Spaces   And\tTabs\nNewlines", "1080p", ""),
        ("Dots...and...more...dots.", "720p", ""),
        ("Title/with\\slashes", "480p", "video"),
        ("Funny Cat Video #cats #funny #viral #pets | By Short Stories", "1080p", ""), // Hashtag removal test
        ("Amazing Dance #TikTok #viral #dance #trending | By Creator Name", "720p", "combined"), // Multiple hashtags test
    ];

    // Create a mock VideoInfo for testing
    let test_video_info = VideoInfo {
        title: String::new(), // Will be overridden in loop
        duration: "5:30 (330 seconds)".to_string(),
        thumbnail: "https://example.com/thumb.jpg".to_string(),
        qualities: Vec::new(),
        video_id: "1234567890".to_string(),
        metadata: VideoMetadata {
            author: "Test Author".to_string(),
            description: "Test description".to_string(),
            publish_date: "2024-01-01".to_string(),
            likes: 100,
            comments: 50,
            views: 1000,
            shares: 25,
            hashtags: vec!["test".to_string(), "video".to_string()],
            duration_seconds: Some(330),
            language: Some("en".to_string()),
            category: Some("test".to_string()),
        },
        extraction_timestamp: chrono::Utc::now(),
        source_url: "https://example.com/test".to_string(),
    };

    for (i, (title, quality, stream_type)) in test_cases.iter().enumerate() {
        println!("\n📝 Test Case {}: {}", i + 1, if title.is_empty() { "[Empty Title]" } else { title });
        println!("   Quality: {}, Stream Type: {}", quality, if stream_type.is_empty() { "[Default]" } else { stream_type });

        let mut video_info = test_video_info.clone();
        video_info.title = title.to_string();

        let filename = extractor.create_descriptive_filename(&video_info, quality, stream_type, "downloads/test", i + 1);
        println!("   ✅ Generated: {}", filename);

        // Extract just the filename part for analysis
        if let Some(file_part) = filename.split('/').last() {
            println!("   📊 Length: {} characters", file_part.len());
            if file_part.len() > 200 {
                println!("   ⚠️ WARNING: Filename exceeds 200 character limit!");
            }

            // Check for invalid characters
            let invalid_chars = ['<', '>', ':', '"', '|', '?', '*', '\\', '/'];
            let has_invalid = file_part.chars().any(|c| invalid_chars.contains(&c));
            if has_invalid {
                println!("   ❌ WARNING: Contains invalid filesystem characters!");
            } else {
                println!("   ✅ Filesystem safe");
            }
        }
    }

    println!("\n🎉 Filename generation testing completed!");
    Ok(())
}

/// Test function to demonstrate edge cases and error handling
#[allow(dead_code)]
fn test_edge_cases() -> std::result::Result<(), Box<dyn std::error::Error>> {
    println!("\n🔬 Testing Edge Cases");
    println!("====================");

    let extractor = FacebookExtractorCLI::new()?;

    // Test sanitization edge cases
    let long_string = "a".repeat(300);
    let edge_cases = vec![
        "...", // Only dots
        "   ", // Only spaces
        "___", // Only underscores
        "CON.mp4", // Reserved name with extension
        "\u{FEFF}BOM Test", // Byte Order Mark
        "Control\x00Chars\x1F", // Control characters
        "🎬🎥📹🎞️🎪", // Only emojis
        &long_string, // Very long string
    ];

    for (i, test_input) in edge_cases.iter().enumerate() {
        println!("\n🧪 Edge Case {}: {:?}", i + 1, test_input);
        let sanitized = extractor.sanitize_filename(test_input);
        println!("   ✅ Sanitized: {:?}", sanitized);
        println!("   📊 Length: {} -> {}", test_input.len(), sanitized.len());
    }
    Ok(())
}
