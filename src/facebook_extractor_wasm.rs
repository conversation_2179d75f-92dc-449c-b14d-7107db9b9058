//! # Facebook Video Extractor WASM Module
//!
//! WASM-compatible implementation of Facebook video extraction functionality.
//! This module provides the same core features as the CLI version but adapted
//! for web browser environments.
//!
//! ## Features
//! - Browser-compatible video extraction using fetch API
//! - CORS proxy integration for cross-origin requests
//! - Stream analysis and metadata extraction
//! - Blob URL generation for downloads
//! - JavaScript interop for browser functionality
//!
//! ## Architecture
//! This module implements the same traits as the CLI version but uses
//! web-compatible APIs instead of native system calls.

use wasm_bindgen::prelude::*;
use wasm_bindgen_futures::JsFuture;
use web_sys::{console, window, Request, RequestInit, RequestMode, Response};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

// Re-export core types from CLI implementation
pub use crate::bin::test_facebook_extractor::{
    VideoInfo, VideoQuality, VideoMetadata, StreamType, StreamCombination,
    FacebookExtractorError, Result, VideoExtractor, StreamDownloader, MetadataParser
};

// ============================================================================
// WASM-SPECIFIC CONFIGURATION
// ============================================================================

/// WASM-specific configuration for browser environment
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WasmConfig {
    /// CORS proxy services for cross-origin requests
    pub proxy_services: Vec<String>,
    /// Enable console logging for debugging
    pub enable_logging: bool,
    /// Maximum retry attempts for failed requests
    pub max_retries: usize,
    /// Request timeout in milliseconds
    pub timeout_ms: u32,
    /// Enable caching in browser storage
    pub enable_caching: bool,
}

impl Default for WasmConfig {
    fn default() -> Self {
        Self {
            proxy_services: vec![
                "https://proxy.cors.sh/".to_string(),
                "https://corsproxy.io/?".to_string(),
                "https://cors.bridged.cc/".to_string(),
            ],
            enable_logging: true,
            max_retries: 3,
            timeout_ms: 30000,
            enable_caching: true,
        }
    }
}

// ============================================================================
// WASM EXTRACTOR IMPLEMENTATION
// ============================================================================

/// WASM-compatible Facebook video extractor
#[wasm_bindgen]
pub struct FacebookExtractorWasm {
    config: WasmConfig,
    cache: HashMap<String, VideoInfo>,
}

#[wasm_bindgen]
impl FacebookExtractorWasm {
    /// Create a new WASM extractor with default configuration
    #[wasm_bindgen(constructor)]
    pub fn new() -> Self {
        Self::with_config(WasmConfig::default())
    }

    /// Create a new WASM extractor with custom configuration
    pub fn with_config(config: WasmConfig) -> Self {
        Self {
            config,
            cache: HashMap::new(),
        }
    }

    /// Extract video information from Facebook URL (WASM-compatible)
    #[wasm_bindgen]
    pub async fn extract_video_info(&self, url: &str) -> Result<JsValue, JsValue> {
        self.log(&format!("🚀 Starting WASM video extraction for: {}", url));

        match self.extract_video_info_internal(url).await {
            Ok(video_info) => {
                self.log("✅ Video extraction successful");
                Ok(serde_wasm_bindgen::to_value(&video_info)?)
            }
            Err(e) => {
                self.log(&format!("❌ Video extraction failed: {}", e));
                Err(JsValue::from_str(&e.to_string()))
            }
        }
    }

    /// Download video stream using browser APIs
    #[wasm_bindgen]
    pub async fn download_stream(&self, quality_js: &JsValue, filename: &str) -> Result<JsValue, JsValue> {
        let quality: VideoQuality = serde_wasm_bindgen::from_value(quality_js.clone())?;
        
        self.log(&format!("📥 Starting download: {} ({})", filename, quality.quality));

        match self.download_stream_internal(&quality, filename).await {
            Ok(blob_url) => {
                self.log("✅ Download successful");
                Ok(JsValue::from_str(&blob_url))
            }
            Err(e) => {
                self.log(&format!("❌ Download failed: {}", e));
                Err(JsValue::from_str(&e.to_string()))
            }
        }
    }

    /// Check if URL is valid Facebook video URL
    #[wasm_bindgen]
    pub fn is_valid_url(&self, url: &str) -> bool {
        url.contains("facebook.com") && (url.contains("/watch") || url.contains("/videos/"))
    }

    /// Extract video ID from Facebook URL
    #[wasm_bindgen]
    pub fn extract_video_id(&self, url: &str) -> Result<String, JsValue> {
        match self.extract_video_id_internal(url) {
            Ok(id) => Ok(id),
            Err(e) => Err(JsValue::from_str(&e.to_string())),
        }
    }

    /// Get cached video info if available
    #[wasm_bindgen]
    pub fn get_cached_info(&self, url: &str) -> Option<JsValue> {
        let cache_key = self.cache_key(url);
        self.cache.get(&cache_key)
            .and_then(|info| serde_wasm_bindgen::to_value(info).ok())
    }

    /// Clear cache
    #[wasm_bindgen]
    pub fn clear_cache(&mut self) {
        self.cache.clear();
        self.log("🗑️ Cache cleared");
    }
}

// ============================================================================
// INTERNAL IMPLEMENTATION
// ============================================================================

impl FacebookExtractorWasm {
    /// Internal video extraction logic
    async fn extract_video_info_internal(&self, url: &str) -> Result<VideoInfo> {
        // Check cache first
        if let Some(cached_info) = self.get_cached_info_internal(url) {
            self.log("📋 Using cached video info");
            return Ok(cached_info);
        }

        // Validate URL
        if !self.is_valid_url(url) {
            return Err(FacebookExtractorError::InvalidUrl(url.to_string()));
        }

        // Extract video ID
        let video_id = self.extract_video_id_internal(url)?;
        self.log(&format!("🆔 Video ID: {}", video_id));

        // Try different extraction methods
        for (i, proxy) in self.config.proxy_services.iter().enumerate() {
            self.log(&format!("🔄 Trying proxy {}/{}: {}", i + 1, self.config.proxy_services.len(), proxy));
            
            match self.try_proxy_extraction(proxy, url).await {
                Ok(mut info) => {
                    info.video_id = video_id;
                    info.source_url = url.to_string();
                    
                    // Cache the result
                    if self.config.enable_caching {
                        self.cache_info(url, info.clone());
                    }
                    
                    return Ok(info);
                }
                Err(e) => {
                    self.log(&format!("⚠️ Proxy {} failed: {}", proxy, e));
                }
            }
        }

        Err(FacebookExtractorError::HtmlParsingError("All extraction methods failed".to_string()))
    }

    /// Try extraction using a specific proxy
    async fn try_proxy_extraction(&self, proxy: &str, url: &str) -> Result<VideoInfo> {
        let proxied_url = format!("{}{}", proxy, url);
        
        // Create fetch request
        let mut opts = RequestInit::new();
        opts.method("GET");
        opts.mode(RequestMode::Cors);

        let request = Request::new_with_str_and_init(&proxied_url, &opts)
            .map_err(|e| FacebookExtractorError::NetworkError(
                reqwest::Error::from(std::io::Error::new(std::io::ErrorKind::Other, format!("{:?}", e)))
            ))?;

        // Add headers
        request.headers().set("User-Agent", 
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
            .map_err(|e| FacebookExtractorError::NetworkError(
                reqwest::Error::from(std::io::Error::new(std::io::ErrorKind::Other, format!("{:?}", e)))
            ))?;

        // Fetch the page
        let window = window().ok_or_else(|| FacebookExtractorError::ConfigError("No window object".to_string()))?;
        let resp_value = JsFuture::from(window.fetch_with_request(&request)).await
            .map_err(|e| FacebookExtractorError::NetworkError(
                reqwest::Error::from(std::io::Error::new(std::io::ErrorKind::Other, format!("{:?}", e)))
            ))?;

        let resp: Response = resp_value.dyn_into()
            .map_err(|e| FacebookExtractorError::NetworkError(
                reqwest::Error::from(std::io::Error::new(std::io::ErrorKind::Other, format!("{:?}", e)))
            ))?;

        // Get response text
        let text_promise = resp.text()
            .map_err(|e| FacebookExtractorError::NetworkError(
                reqwest::Error::from(std::io::Error::new(std::io::ErrorKind::Other, format!("{:?}", e)))
            ))?;

        let text_value = JsFuture::from(text_promise).await
            .map_err(|e| FacebookExtractorError::NetworkError(
                reqwest::Error::from(std::io::Error::new(std::io::ErrorKind::Other, format!("{:?}", e)))
            ))?;

        let html = text_value.as_string()
            .ok_or_else(|| FacebookExtractorError::HtmlParsingError("Failed to get response text".to_string()))?;

        // Parse HTML for video information
        self.parse_facebook_html(&html, url)
    }

    /// Parse Facebook HTML for video URLs (simplified for WASM)
    fn parse_facebook_html(&self, html: &str, original_url: &str) -> Result<VideoInfo> {
        self.log("🔍 Parsing HTML for video URLs...");

        // Use similar parsing logic as CLI but simplified for WASM
        let video_id = self.extract_video_id_internal(original_url)?;
        
        // Extract video URLs using regex patterns
        let video_urls = self.extract_video_urls_from_html(html)?;
        
        if video_urls.is_empty() {
            return Err(FacebookExtractorError::HtmlParsingError("No video URLs found".to_string()));
        }

        // Analyze streams
        let qualities: Vec<VideoQuality> = video_urls.into_iter()
            .map(|url| self.analyze_video_stream(&url))
            .collect();

        // Extract metadata
        let title = self.extract_title_from_html(html, &video_id);
        let metadata = self.extract_basic_metadata(html);

        Ok(VideoInfo {
            title,
            duration: "Unknown".to_string(),
            thumbnail: String::new(),
            qualities,
            video_id,
            metadata,
            extraction_timestamp: chrono::Utc::now(),
            source_url: original_url.to_string(),
        })
    }

    /// Extract video URLs from HTML (simplified)
    fn extract_video_urls_from_html(&self, html: &str) -> Result<Vec<String>> {
        // Simplified URL extraction for WASM
        let patterns = vec![
            r#""playable_url":"([^"]*\.mp4[^"]*)""#,
            r#""browser_native_hd_url":"([^"]*\.mp4[^"]*)"#,
            r#""browser_native_sd_url":"([^"]*\.mp4[^"]*)"#,
        ];

        let mut urls = Vec::new();
        
        for pattern in patterns {
            if let Ok(regex) = regex::Regex::new(pattern) {
                for capture in regex.captures_iter(html) {
                    if let Some(url_match) = capture.get(1) {
                        let url = url_match.as_str().replace("\\", "");
                        if !urls.contains(&url) {
                            urls.push(url);
                        }
                    }
                }
            }
        }

        Ok(urls)
    }

    /// Analyze video stream (simplified for WASM)
    fn analyze_video_stream(&self, url: &str) -> VideoQuality {
        // Simplified stream analysis
        let (quality, width, height) = if url.contains("hd") {
            ("720p".to_string(), 1280, 720)
        } else {
            ("480p".to_string(), 854, 480)
        };

        VideoQuality {
            quality,
            size: "Unknown".to_string(),
            format: "MP4".to_string(),
            download_url: url.to_string(),
            width,
            height,
            stream_type: StreamType::CompleteVideoAudio,
            efg_metadata: String::new(),
            estimated_size_mb: 0,
            bitrate_kbps: None,
            fps: None,
            codec: None,
        }
    }

    /// Download stream using browser APIs
    async fn download_stream_internal(&self, quality: &VideoQuality, filename: &str) -> Result<String> {
        // Create fetch request for video download
        let request = Request::new_with_str(&quality.download_url)
            .map_err(|e| FacebookExtractorError::NetworkError(
                reqwest::Error::from(std::io::Error::new(std::io::ErrorKind::Other, format!("{:?}", e)))
            ))?;

        let window = window().ok_or_else(|| FacebookExtractorError::ConfigError("No window object".to_string()))?;
        let resp_value = JsFuture::from(window.fetch_with_request(&request)).await
            .map_err(|e| FacebookExtractorError::NetworkError(
                reqwest::Error::from(std::io::Error::new(std::io::ErrorKind::Other, format!("{:?}", e)))
            ))?;

        let resp: Response = resp_value.dyn_into()
            .map_err(|e| FacebookExtractorError::NetworkError(
                reqwest::Error::from(std::io::Error::new(std::io::ErrorKind::Other, format!("{:?}", e)))
            ))?;

        // Get blob from response
        let blob_promise = resp.blob()
            .map_err(|e| FacebookExtractorError::NetworkError(
                reqwest::Error::from(std::io::Error::new(std::io::ErrorKind::Other, format!("{:?}", e)))
            ))?;

        let blob_value = JsFuture::from(blob_promise).await
            .map_err(|e| FacebookExtractorError::NetworkError(
                reqwest::Error::from(std::io::Error::new(std::io::ErrorKind::Other, format!("{:?}", e)))
            ))?;

        // Create blob URL for download
        let url_obj = web_sys::Url::create_object_url_with_blob(&blob_value.into())
            .map_err(|e| FacebookExtractorError::DownloadError(format!("Failed to create blob URL: {:?}", e)))?;

        Ok(url_obj)
    }

    // Helper methods
    fn extract_video_id_internal(&self, url: &str) -> Result<String> {
        // Same logic as CLI implementation
        let patterns = [
            r"facebook\.com/watch\?.*v=(\d+)",
            r"facebook\.com/.*/videos/(\d+)",
            r"facebook\.com/reel/(\d+)",
        ];

        for pattern in &patterns {
            if let Ok(regex) = regex::Regex::new(pattern) {
                if let Some(capture) = regex.captures(url) {
                    if let Some(id_match) = capture.get(1) {
                        return Ok(id_match.as_str().to_string());
                    }
                }
            }
        }

        Err(FacebookExtractorError::VideoIdExtraction("Could not extract video ID".to_string()))
    }

    fn extract_title_from_html(&self, html: &str, video_id: &str) -> String {
        // Simplified title extraction
        if let Ok(regex) = regex::Regex::new(r#"<title>([^<]+)</title>"#) {
            if let Some(capture) = regex.captures(html) {
                if let Some(title_match) = capture.get(1) {
                    return title_match.as_str().to_string();
                }
            }
        }
        format!("Facebook Video {}", &video_id[..8.min(video_id.len())])
    }

    fn extract_basic_metadata(&self, _html: &str) -> VideoMetadata {
        // Return default metadata for WASM (can be enhanced later)
        VideoMetadata::default()
    }

    fn get_cached_info_internal(&self, url: &str) -> Option<VideoInfo> {
        if !self.config.enable_caching {
            return None;
        }
        let cache_key = self.cache_key(url);
        self.cache.get(&cache_key).cloned()
    }

    fn cache_info(&mut self, url: &str, info: VideoInfo) {
        if self.config.enable_caching {
            let cache_key = self.cache_key(url);
            self.cache.insert(cache_key, info);
        }
    }

    fn cache_key(&self, url: &str) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};
        let mut hasher = DefaultHasher::new();
        url.hash(&mut hasher);
        format!("wasm_video_{}", hasher.finish())
    }

    fn log(&self, message: &str) {
        if self.config.enable_logging {
            console::log_1(&JsValue::from_str(message));
        }
    }
}
